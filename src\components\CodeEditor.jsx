import { useRef, useEffect } from 'react'
import Editor from '@monaco-editor/react'
import './CodeEditor.css'

const CodeEditor = ({ value, onChange, language = 'javascript', forceReload }) => {
  const editorRef = useRef(null)
  const containerRef = useRef(null)

  const handleEditorDidMount = (editor, monaco) => {
    editorRef.current = editor

    // Configure Monaco for Three.js
    monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.ES2020,
      allowNonTsExtensions: true,
      moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
      module: monaco.languages.typescript.ModuleKind.CommonJS,
      noEmit: true,
      esModuleInterop: true,
      jsx: monaco.languages.typescript.JsxEmit.React,
      reactNamespace: 'React',
      allowJs: true,
      typeRoots: ['node_modules/@types']
    })

    // Add Three.js type definitions (basic ones)
    const threeJsTypes = `
      declare module 'three' {
        export class Scene {
          constructor();
          add(object: Object3D): void;
          remove(object: Object3D): void;
        }
        
        export class PerspectiveCamera {
          constructor(fov?: number, aspect?: number, near?: number, far?: number);
          position: Vector3;
          lookAt(vector: Vector3): void;
        }
        
        export class WebGLRenderer {
          constructor(parameters?: any);
          setSize(width: number, height: number): void;
          render(scene: Scene, camera: Camera): void;
          domElement: HTMLCanvasElement;
        }
        
        export class BoxGeometry {
          constructor(width?: number, height?: number, depth?: number);
        }
        
        export class SphereGeometry {
          constructor(radius?: number, widthSegments?: number, heightSegments?: number);
        }
        
        export class PlaneGeometry {
          constructor(width?: number, height?: number);
        }
        
        export class MeshBasicMaterial {
          constructor(parameters?: any);
          color: Color;
        }
        
        export class MeshStandardMaterial {
          constructor(parameters?: any);
          color: Color;
        }
        
        export class Mesh {
          constructor(geometry?: any, material?: any);
          position: Vector3;
          rotation: Euler;
          scale: Vector3;
        }
        
        export class Vector3 {
          constructor(x?: number, y?: number, z?: number);
          x: number;
          y: number;
          z: number;
          set(x: number, y: number, z: number): Vector3;
        }
        
        export class Euler {
          constructor(x?: number, y?: number, z?: number);
          x: number;
          y: number;
          z: number;
        }
        
        export class Color {
          constructor(color?: any);
          setHex(hex: number): Color;
          setRGB(r: number, g: number, b: number): Color;
        }
        
        export class DirectionalLight {
          constructor(color?: any, intensity?: number);
          position: Vector3;
        }
        
        export class AmbientLight {
          constructor(color?: any, intensity?: number);
        }
        
        export class Clock {
          constructor();
          getElapsedTime(): number;
          getDelta(): number;
        }
        
        export type Object3D = any;
        export type Camera = any;
      }
    `

    monaco.languages.typescript.javascriptDefaults.addExtraLib(
      threeJsTypes,
      'three.d.ts'
    )

    // Set editor theme
    monaco.editor.setTheme('vs-dark')
  }

  const handleEditorChange = (value) => {
    if (onChange) {
      onChange(value || '')
    }
  }

  // Force reload editor when forceReload prop changes
  useEffect(() => {
    if (forceReload && editorRef.current) {
      // Force a complete layout recalculation
      setTimeout(() => {
        editorRef.current.layout({ width: 0, height: 0 })
        setTimeout(() => {
          editorRef.current.layout()
        }, 10)
      }, 50)
    }
  }, [forceReload])

  return (
    <div className="code-editor" ref={containerRef}>
      <Editor
        height="100%"
        language={language}
        value={value || ''}
        onChange={handleEditorChange}
        onMount={handleEditorDidMount}
        theme="vs-dark"
        options={{
          minimap: { enabled: false },
          fontSize: 14,
          lineNumbers: 'on',
          roundedSelection: false,
          scrollBeyondLastLine: false,
          automaticLayout: true,
          tabSize: 2,
          insertSpaces: true,
          wordWrap: 'on',
          contextmenu: true,
          selectOnLineNumbers: true,
          lineDecorationsWidth: 0,
          lineNumbersMinChars: 3,
          glyphMargin: false,
          folding: false,
          renderLineHighlight: 'line',
          scrollBeyondLastLine: true,
          smoothScrolling: true,
          scrollbar: {
            vertical: 'auto',
            horizontal: 'auto',
            useShadows: false,
            verticalHasArrows: false,
            horizontalHasArrows: false,
            verticalScrollbarSize: 12,
            horizontalScrollbarSize: 12,
            alwaysConsumeMouseWheel: false,
            handleMouseWheel: true,
            mouseWheelScrollSensitivity: 1
          }
        }}
      />
    </div>
  )
}

export default CodeEditor
