.home {
  background: #0a0a0a;
  color: #ffffff;
  height: 100vh;
  width: 100vw;
  overflow-y: auto;
  overflow-x: hidden;
}

.container {
  max-width: 90vw;
  margin: 0 auto;
  padding: 0 2vw;
}

/* Hero Section */
.hero {
  padding: 8vh 4vw;
  text-align: center;
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border-bottom: 1px solid #333;
}

.hero-content {
  max-width: 60vw;
  margin: 0 auto;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.highlight {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 1.25rem;
  color: #9ca3af;
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 2rem;
  border-radius: 0.5rem;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s;
  border: 2px solid transparent;
}

.cta-button.primary {
  background: #3b82f6;
  color: #ffffff;
}

.cta-button.primary:hover {
  background: #2563eb;
  transform: translateY(-2px);
}

.cta-button.secondary {
  background: transparent;
  color: #9ca3af;
  border-color: #374151;
}

.cta-button.secondary:hover {
  color: #ffffff;
  border-color: #6b7280;
}

/* Features Section */
.features {
  padding: 4rem 0;
}

.features h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #ffffff;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: #1a1a1a;
  padding: 2rem;
  border-radius: 0.75rem;
  border: 1px solid #333;
  text-align: center;
  transition: all 0.2s;
}

.feature-card:hover {
  transform: translateY(-4px);
  border-color: #3b82f6;
}

.feature-icon {
  color: #3b82f6;
  margin-bottom: 1rem;
}

.feature-card h3 {
  font-size: 1.25rem;
  margin-bottom: 1rem;
  color: #ffffff;
}

.feature-card p {
  color: #9ca3af;
  line-height: 1.6;
}

/* Quick Start Section */
.quick-start {
  padding: 4rem 0;
  background: #111111;
}

.quick-start h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #ffffff;
}

.quick-start > .container > p {
  text-align: center;
  color: #9ca3af;
  margin-bottom: 3rem;
  font-size: 1.125rem;
}

.quick-start-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.quick-start-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: #1a1a1a;
  padding: 1.5rem;
  border-radius: 0.75rem;
  border: 1px solid #333;
  text-decoration: none;
  color: #ffffff;
  transition: all 0.2s;
}

.quick-start-card:hover {
  transform: translateY(-2px);
  border-color: #3b82f6;
}

.lesson-number {
  background: #3b82f6;
  color: #ffffff;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.125rem;
  flex-shrink: 0;
}

.lesson-content {
  flex: 1;
}

.lesson-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
}

.lesson-content p {
  margin: 0;
  color: #9ca3af;
  font-size: 0.875rem;
}

.lesson-arrow {
  color: #6b7280;
  flex-shrink: 0;
}

/* Learning Path Section */
.learning-path {
  padding: 4rem 0;
}

.learning-path h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: #ffffff;
}

.path-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.path-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 200px;
}

.step-number {
  background: #3b82f6;
  color: #ffffff;
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.step-content h3 {
  margin: 0 0 0.5rem 0;
  color: #ffffff;
}

.step-content p {
  margin: 0;
  color: #9ca3af;
  font-size: 0.875rem;
}

.path-connector {
  width: 3rem;
  height: 2px;
  background: #374151;
  margin: 0 1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-description {
    font-size: 1.125rem;
  }
  
  .path-steps {
    flex-direction: column;
  }
  
  .path-connector {
    width: 2px;
    height: 3rem;
    margin: 1rem 0;
  }
}
