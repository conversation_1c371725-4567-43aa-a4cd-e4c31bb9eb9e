.help-trigger {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  background: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  transition: all 0.2s;
  z-index: 1000;
}

.help-trigger:hover {
  background: #2563eb;
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.5);
}

.help-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 2rem;
}

.help-modal {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 0.75rem;
  width: 100%;
  max-width: 800px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.help-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #333;
  background: #2a2a2a;
}

.help-header h2 {
  margin: 0;
  color: #ffffff;
  font-size: 1.25rem;
}

.help-close {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.help-close:hover {
  color: #ffffff;
  background: #3a3a3a;
}

.help-tabs {
  display: flex;
  background: #2a2a2a;
  border-bottom: 1px solid #333;
}

.help-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.help-tab:hover {
  color: #ffffff;
  background: #3a3a3a;
}

.help-tab.active {
  color: #3b82f6;
  background: #1a1a1a;
  border-bottom: 2px solid #3b82f6;
}

.help-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  color: #ffffff;
}

.help-content h3 {
  margin: 0 0 1rem 0;
  color: #3b82f6;
  font-size: 1.125rem;
}

.help-content h4 {
  margin: 0 0 0.5rem 0;
  color: #ffffff;
  font-size: 1rem;
}

/* Shortcuts */
.shortcuts-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.shortcut-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: #2a2a2a;
  border-radius: 0.375rem;
}

.shortcut-item kbd {
  background: #3a3a3a;
  color: #ffffff;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
  min-width: 100px;
  text-align: center;
  border: 1px solid #4a4a4a;
}

.shortcut-item span {
  color: #d1d5db;
  font-size: 0.875rem;
}

/* Patterns */
.patterns-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.pattern-item {
  background: #2a2a2a;
  padding: 1rem;
  border-radius: 0.375rem;
  border: 1px solid #333;
}

.pattern-item pre {
  background: #1e1e1e;
  padding: 1rem;
  border-radius: 0.375rem;
  overflow-x: auto;
  margin: 0.5rem 0 0 0;
  border: 1px solid #333;
}

.pattern-item code {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  color: #e5e7eb;
  white-space: pre;
}

/* Troubleshooting */
.troubleshooting-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.troubleshooting-item {
  background: #2a2a2a;
  padding: 1rem;
  border-radius: 0.375rem;
  border: 1px solid #333;
}

.troubleshooting-item ul {
  margin: 0.5rem 0 0 0;
  padding-left: 1.5rem;
  color: #d1d5db;
}

.troubleshooting-item li {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.4;
}

/* Resources */
.resources-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.resource-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: #2a2a2a;
  border-radius: 0.375rem;
  border: 1px solid #333;
  text-decoration: none;
  color: #ffffff;
  transition: all 0.2s;
}

.resource-item:hover {
  background: #3a3a3a;
  border-color: #3b82f6;
  transform: translateY(-1px);
}

.resource-content {
  flex: 1;
}

.resource-content h4 {
  color: #3b82f6;
  margin-bottom: 0.25rem;
}

.resource-content p {
  color: #9ca3af;
  font-size: 0.875rem;
  margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .help-overlay {
    padding: 1rem;
  }
  
  .help-modal {
    max-height: 90vh;
  }
  
  .help-tabs {
    flex-wrap: wrap;
  }
  
  .help-tab {
    flex: 1 1 50%;
    min-width: 120px;
  }
  
  .help-content {
    padding: 1rem;
  }
  
  .shortcut-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .shortcut-item kbd {
    min-width: auto;
  }
}
