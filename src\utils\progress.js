// Progress tracking utilities
const STORAGE_KEY = 'threejs-academy-progress'

export const getProgress = () => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    return stored ? JSON.parse(stored) : {}
  } catch (error) {
    console.error('Error loading progress:', error)
    return {}
  }
}

export const saveProgress = (progress) => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(progress))
  } catch (error) {
    console.error('Error saving progress:', error)
  }
}

export const markLessonCompleted = (lessonId) => {
  const progress = getProgress()
  progress[lessonId] = {
    completed: true,
    completedAt: new Date().toISOString(),
    ...progress[lessonId]
  }
  saveProgress(progress)
}

export const markLessonStarted = (lessonId) => {
  const progress = getProgress()
  if (!progress[lessonId]) {
    progress[lessonId] = {
      started: true,
      startedAt: new Date().toISOString()
    }
    saveProgress(progress)
  }
}

export const saveLessonCode = (lessonId, code) => {
  const progress = getProgress()
  progress[lessonId] = {
    ...progress[lessonId],
    savedCode: code,
    lastSaved: new Date().toISOString()
  }
  saveProgress(progress)
}

export const getLessonCode = (lessonId) => {
  const progress = getProgress()
  return progress[lessonId]?.savedCode || null
}

export const isLessonCompleted = (lessonId) => {
  const progress = getProgress()
  return progress[lessonId]?.completed || false
}

export const isLessonStarted = (lessonId) => {
  const progress = getProgress()
  return progress[lessonId]?.started || false
}

export const getCompletionPercentage = () => {
  const progress = getProgress()
  const totalLessons = 11 // Updated to include all lessons
  const completedLessons = Object.values(progress).filter(p => p.completed).length
  return Math.round((completedLessons / totalLessons) * 100)
}

export const getNextLesson = () => {
  const progress = getProgress()
  for (let i = 1; i <= 11; i++) {
    if (!isLessonCompleted(i)) {
      return i
    }
  }
  return null // All lessons completed
}

export const clearProgress = () => {
  try {
    localStorage.removeItem(STORAGE_KEY)
  } catch (error) {
    console.error('Error clearing progress:', error)
  }
}
