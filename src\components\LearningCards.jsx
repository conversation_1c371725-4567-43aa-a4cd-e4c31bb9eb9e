import { useState, useEffect, useRef } from 'react'
import { Play, ChevronDown } from 'lucide-react'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism'
import MarkdownRenderer from './MarkdownRenderer'
import './LearningCards.css'

const LearningCards = ({ lesson, onCodeInsert }) => {
  const [selectedStep, setSelectedStep] = useState(0)
  const [isMobile, setIsMobile] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const stepContentRef = useRef(null)

  // Detect mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isDropdownOpen && !event.target.closest('.step-dropdown')) {
        setIsDropdownOpen(false)
      }
    }

    if (isDropdownOpen) {
      document.addEventListener('click', handleClickOutside)
      return () => document.removeEventListener('click', handleClickOutside)
    }
  }, [isDropdownOpen])

  // Scroll to top when step changes
  useEffect(() => {
    if (stepContentRef.current) {
      stepContentRef.current.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    }
  }, [selectedStep])

  // Get learning steps from lesson data
  const getSteps = () => {
    if (!lesson.learningPath || lesson.learningPath.length === 0) {
      return [{
        id: 'overview',
        title: lesson.title,
        type: 'overview',
        content: lesson.description
      }]
    }
    return lesson.learningPath
  }

  const steps = getSteps()
  const currentStep = steps[selectedStep]

  const handleStepClick = (index) => {
    setSelectedStep(index)
    if (isMobile) {
      setIsDropdownOpen(false) // Close dropdown on mobile after selection
    }
  }

  const handleCodeInsert = (code) => {
    if (onCodeInsert) {
      onCodeInsert(code)
    }
  }

  return (
    <div className="compact-learning">
      {/* Step Navigation - Dropdown on Mobile, List on Desktop */}
      {isMobile ? (
        <div className="step-dropdown">
          <button
            className="dropdown-trigger"
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          >
            <span className="current-step">
              <span className="step-num">{selectedStep + 1}</span>
              <span className="step-title">{currentStep.title}</span>
            </span>
            <ChevronDown
              size={16}
              className={`dropdown-icon ${isDropdownOpen ? 'open' : ''}`}
            />
          </button>

          {isDropdownOpen && (
            <div className="dropdown-menu">
              {steps.map((step, index) => (
                <div
                  key={step.id}
                  className={`dropdown-item ${index === selectedStep ? 'active' : ''}`}
                  onClick={() => handleStepClick(index)}
                >
                  <span className="step-num">{index + 1}</span>
                  <span className="step-title">{step.title}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      ) : (
        <div className="step-list">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={`step-item ${index === selectedStep ? 'active' : ''}`}
              onClick={() => handleStepClick(index)}
            >
              <span className="step-num">{index + 1}</span>
              <span className="step-title">{step.title}</span>
            </div>
          ))}
        </div>
      )}

      {/* Current Step Content */}
      <div className="step-content" ref={stepContentRef}>
        <div className="step-header">
          <h4>{currentStep.title}</h4>
          <span className="step-type">{currentStep.type}</span>
        </div>

        <div className="step-body">
          <MarkdownRenderer content={currentStep.content} />

          {currentStep.codeExample && (
            <div className="code-section">
              <div className="code-block">
                <SyntaxHighlighter
                  language="javascript"
                  style={vscDarkPlus}
                  customStyle={{
                    margin: 0,
                    padding: '8px',
                    background: '#0a0a0a',
                    fontSize: '11px',
                    lineHeight: '1.4',
                    borderRadius: '4px',
                    maxHeight: '120px',
                    overflow: 'auto'
                  }}
                  showLineNumbers={false}
                  wrapLines={true}
                >
                  {currentStep.codeExample}
                </SyntaxHighlighter>
              </div>
              <button
                className="run-btn"
                onClick={() => handleCodeInsert(currentStep.codeExample)}
              >
                <Play size={12} />
                Run
              </button>
            </div>
          )}

          {currentStep.explanation && (
            <div className="explanation">
              <strong>💡</strong> {currentStep.explanation}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default LearningCards
