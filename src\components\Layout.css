.layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background: #0a0a0a;
  color: #ffffff;
  position: relative;
  overflow: hidden;
}

/* Header */
.header {
  background: #1a1a1a;
  border-bottom: 1px solid #333;
  z-index: 1000;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 8vh;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 1rem;
  height: 100%;
  max-width: 100%;
}

.sidebar-toggle {
  position: fixed;
  top: 12px;
  left: 12px;
  background: rgba(26, 26, 26, 0.9);
  border: 1px solid #333;
  color: #ffffff;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s;
  z-index: 1001;
  backdrop-filter: blur(8px);
}

.sidebar-toggle:hover {
  background: rgba(51, 51, 51, 0.9);
  border-color: #555;
}

.sidebar-toggle:hover {
  background: #333;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: #ffffff;
  font-size: 1.25rem;
  font-weight: 600;
}

.logo:hover {
  color: #60a5fa;
}



/* Main Layout Container */
.main-layout {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100vw;
  height: 92vh;
  margin-top: 8vh;
  position: relative;
  overflow: hidden;
}

.main-content {
  flex: 1;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

/* Sidebar - Overlay Menu */
.sidebar {
  position: fixed;
  top: 8vh;
  left: 0;
  width: 25vw;
  height: 92vh;
  background: #1a1a1a;
  border-right: 1px solid #333;
  overflow: hidden;
  box-shadow: 4px 0 16px rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  z-index: 999;
  transform: translateX(0);
  transition: transform 0.3s ease;
}

/* Sidebar Header */
.sidebar-header {
  background: #2a2a2a;
  border-bottom: 1px solid #333;
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-title {
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.sidebar-close {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.sidebar-close:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
}

.sidebar-content {
  padding: 1.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

/* Custom Scrollbar for Sidebar */
.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: #1a1a1a;
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: #505050;
}

.sidebar-content h3 {
  margin: 0 0 1.5rem 0;
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* Progress Section */
.progress-section {
  background: #252525;
  padding: 1.5rem;
  border-radius: 0.75rem;
  border: 1px solid #333;
}

.progress-section h3 {
  margin: 0 0 1rem 0;
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
}

.progress-bar {
  width: 100%;
  background: #333;
  border-radius: 0.5rem;
  height: 10px;
  margin: 0.75rem 0;
  overflow: hidden;
  border: 1px solid #444;
}

.progress-fill {
  background: linear-gradient(90deg, #10b981, #3b82f6);
  height: 100%;
  border-radius: 0.5rem;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.9rem;
  color: #10b981;
  font-weight: 600;
  text-align: center;
  display: block;
}

/* Lessons Section */
.lessons-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.lessons-section h3 {
  margin: 0 0 1rem 0;
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
}

.lessons-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  overflow-y: auto;
  flex: 1;
  padding-right: 0.5rem;
}

.lesson-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  margin-bottom: 0.75rem;
  text-decoration: none;
  color: #9ca3af;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  background: #252525;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.lesson-item:hover {
  background: #2a2a2a;
  color: #ffffff;
  border-color: #444;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.lesson-item.active {
  background: #1e40af;
  color: #ffffff;
  border-color: #3b82f6;
}

.lesson-item.completed {
  border-left: 3px solid #10b981;
}

.lesson-item.completed .lesson-title {
  color: #10b981;
}

.lesson-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.lesson-title {
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.completion-icon {
  color: #10b981;
  flex-shrink: 0;
}

.lesson-category {
  font-size: 0.75rem;
  color: #6b7280;
}

.lesson-difficulty {
  font-size: 0.75rem;
  font-weight: 500;
}



/* Overlay */
.sidebar-overlay {
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
  backdrop-filter: blur(2px);
}

/* Mobile-First Responsive Design */

/* Large screens */
@media (min-width: 1400px) {
  .sidebar {
    width: 20vw;
  }
}

/* Desktop */
@media (max-width: 1200px) {
  .sidebar {
    width: 30vw;
  }
}

/* Tablets */
@media (max-width: 1024px) {
  .sidebar {
    width: 35vw;
  }

  .sidebar-content {
    padding: 1.25rem;
  }

  .lesson-item {
    padding: 0.875rem 1rem;
  }
}

/* Mobile landscape and small tablets */
@media (max-width: 768px) {
  .layout {
    overflow-x: hidden;
  }

  .header {
    height: 7vh;
  }

  .main-layout {
    height: 93vh;
    margin-top: 7vh;
  }

  .sidebar {
    top: 7vh;
    height: 93vh;
    width: 45vw;
  }

  .sidebar-content {
    padding: 1rem;
  }

  .header-content {
    padding: 0 1rem;
  }

  .logo {
    font-size: 1.1rem;
  }

  .lesson-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
  }

  .lesson-title {
    font-size: 0.9rem;
  }

  .lesson-category,
  .lesson-difficulty {
    font-size: 0.7rem;
  }

  .progress-section {
    padding: 1.25rem;
  }
}

/* Mobile portrait */
@media (max-width: 480px) {
  .header {
    height: 6vh;
  }

  .main-layout {
    height: 94vh;
    margin-top: 6vh;
  }

  .sidebar {
    top: 6vh;
    height: 94vh;
    width: 100vw;
    left: 0;
    right: 0;
  }

  .sidebar-content {
    padding: 0.75rem;
  }

  .header-content {
    padding: 0 0.75rem;
  }

  .logo {
    font-size: 1rem;
  }

  .sidebar-toggle {
    top: 8px;
    left: 8px;
    padding: 6px;
  }

  .lesson-item {
    padding: 0.625rem;
    margin-bottom: 0.375rem;
  }

  .lesson-title {
    font-size: 0.85rem;
  }

  .lesson-info {
    gap: 0.125rem;
  }

  .lesson-category,
  .lesson-difficulty {
    font-size: 0.65rem;
  }

  .progress-section {
    padding: 1rem;
  }

  .progress-section h3 {
    font-size: 1rem;
  }

  .progress-text {
    font-size: 0.8rem;
  }

  .sidebar-header {
    padding: 0.75rem 1rem;
  }

  .sidebar-title {
    font-size: 1rem;
  }
}

/* Very small screens */
@media (max-width: 360px) {
  .header-content {
    padding: 0 0.5rem;
  }

  .logo {
    font-size: 0.9rem;
    gap: 0.25rem;
  }

  .sidebar-content {
    padding: 0.5rem;
  }

  .lesson-item {
    padding: 0.5rem;
  }

  .lesson-title {
    font-size: 0.8rem;
  }

  .progress-section {
    padding: 0.75rem;
  }

  .sidebar-header {
    padding: 0.5rem 0.75rem;
  }
}

/* Ensure proper touch targets on mobile */
@media (max-width: 768px) {
  .lesson-item,
  .sidebar-toggle,
  .sidebar-close {
    min-height: 44px;
    min-width: 44px;
  }

  .lesson-item {
    min-height: auto;
    padding: 0.75rem;
  }
}

/* Landscape orientation adjustments */
@media (max-width: 768px) and (orientation: landscape) {
  .sidebar {
    width: 40vw;
  }
}

@media (max-width: 480px) and (orientation: landscape) {
  .sidebar {
    width: 60vw;
  }
}



/* Difficulty colors */
.text-green-500 { color: #10b981; }
.text-yellow-500 { color: #f59e0b; }
.text-red-500 { color: #ef4444; }
.text-gray-500 { color: #6b7280; }
