// Lesson data structure and content
const lessons = {
  1: {
    id: 1,
    title: "Introduction to Three.js",
    difficulty: "Beginner",
    category: "Introduction",
    description: "Explore what Three.js is and why it matters in web graphics. Learn about its purpose, benefits, and discover the rich ecosystem of resources available.",
    objectives: [
      "Explain what Three.js is and why it matters in web graphics",
      "Identify common use cases for Three.js applications",
      "Navigate key resources (official docs, examples gallery)",
      "Recognize shared architectural patterns in Three.js demos"
    ],
    theory: [
      "Three.js is a JavaScript library that provides an abstraction over raw WebGL for easier 3D development. It handles the complex WebGL setup and provides intuitive APIs.",
      "Core benefits include cross-browser compatibility, active community support, and rich examples that demonstrate best practices and creative possibilities.",
      "Common use cases include interactive data visualization, web-based games, architectural walkthroughs, product showcases, and artistic installations.",
      "The Three.js community offers extensive resources including official documentation, examples gallery, forums, and learning materials to support developers."
    ],
    concepts: [
      {
        name: "Library Purpose",
        description: "Abstraction over raw WebGL for easier 3D development",
        example: "Three.js simplifies complex WebGL operations into intuitive JavaScript APIs"
      },
      {
        name: "Core Benefits",
        description: "Cross-browser compatibility, active community, rich examples",
        example: "Works consistently across browsers with extensive community support"
      },
      {
        name: "Use Case Survey",
        description: "Interactive data viz, games, architectural walkthroughs",
        example: "From simple 3D charts to complex virtual reality experiences"
      },
      {
        name: "Community Resources",
        description: "Official examples, online sandboxes, forums",
        example: "threejs.org/examples, documentation, and community forums"
      }
    ],
    learningPath: [
      {
        id: "step1-what-is-threejs",
        title: "Step 1: What is Three.js?",
        type: "concept",
        content: "**Three.js** is a JavaScript library that makes creating 3D graphics on the web easy and accessible. 🚀 Before Three.js, creating 3D graphics required writing hundreds of lines of complex WebGL code.\n\n🔧 **WHAT THREE.JS SOLVES:**\n📝 Managing vertex buffers and geometry data\n🎨 Compiling and linking shader programs\n🧮 Handling complex matrix mathematics\n🌐 Dealing with browser compatibility issues\n⚡ Optimizing performance across different devices\n\n💡 **WEBGL VS THREE.JS COMPARISON:**\n🔹 **WebGL:** Assembly language for graphics - incredibly powerful but extremely difficult\n🔹 **Three.js:** High-level programming language that translates creative ideas into optimized WebGL\n🔹 **Example:** WebGL needs 200+ lines for a simple cube, Three.js does it in 4 lines\n\n🌍 **CROSS-BROWSER COMPATIBILITY:**\n✅ Different browsers implement WebGL slightly differently\n✅ Three.js smooths out these differences automatically\n✅ Your 3D graphics work consistently everywhere\n✅ No need to test and fix browser-specific issues\n\n🛠️ **ADDITIONAL BENEFITS:**\n🐛 Helpful debugging tools and error messages\n⚡ Built-in performance optimizations\n🔌 Rich ecosystem of plugins and extensions\n👥 Active community and extensive documentation\n🏆 Battle-tested in thousands of websites\n\n🎯 **USE CASES:**\n🛍️ Product showcases and e-commerce\n📊 Data visualizations and infographics\n🎮 Browser games and interactive experiences\n🏗️ Architectural walkthroughs\n🎨 Creative coding and digital art",
        explanation: "Imagine you want to build a house. WebGL is like having raw materials - lumber, nails, concrete - but no tools or instructions. Three.js is like having a complete toolkit with power tools, blueprints, and step-by-step guides. Both can build the same house, but one makes the process enjoyable and accessible.",
        codeExample: `// Create your first 3D object with just 4 lines!
const geometry = new THREE.BoxGeometry(1, 1, 1);
const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
const cube = new THREE.Mesh(geometry, material);
scene.add(cube);

// Position the camera to see our cube
camera.position.z = 3;

// That's it! You've created a 3D cube
// Without Three.js, this would take hundreds of lines of WebGL code

console.log('🎉 Your first 3D object is ready!');`,
        nextStep: "Next: Discover what you can build with Three.js"
      },
      {
        id: "step2-amazing-examples",
        title: "Step 2: Amazing Things You Can Build",
        type: "exploration",
        content: "Three.js powers an incredible variety of web experiences that were impossible just a few years ago! 🌟 Let's explore the amazing things you can build.\n\n📊 **INTERACTIVE DATA VISUALIZATIONS:**\n🔍 Transform boring spreadsheets into engaging 3D charts\n✈️ Fly through your data and discover hidden patterns\n🏢 **Real companies using this:** Google, NASA, Bloomberg\n💡 **Why it works:** 3D reveals relationships invisible in 2D graphs\n💻 `new THREE.SphereGeometry()` // Each data point becomes a 3D object\n\n🎮 **BROWSER GAMES:**\n🎯 First-person shooters, puzzle games, racing simulators\n🚀 VR experiences running directly in browsers\n⚡ **No downloads required** - players click and play instantly\n🏆 **Quality level:** Rivals desktop games in visual fidelity\n💻 `new THREE.PerspectiveCamera()` // Creates immersive viewpoints\n\n🏛️ **VIRTUAL TOURS & ARCHITECTURE:**\n🌍 Explore spaces thousands of miles away\n🏠 **Real estate:** Walk through homes before they're built\n🖼️ **Museums:** Virtual exhibitions reaching global audiences\n🎨 **Benefits:** Accessible, cost-effective, always available\n💻 `new THREE.TextureLoader()` // Loads realistic surface materials\n\n🎨 **CREATIVE & ARTISTIC PROJECTS:**\n🎵 Interactive sculptures responding to music\n🌱 Generative art evolving over time\n🌈 Immersive experiences blurring digital/physical reality\n✨ **Possibilities:** Limited only by imagination\n💻 `new THREE.ShaderMaterial()` // Custom visual effects\n\n🛍️ **PRODUCT SHOWCASES & E-COMMERCE:**\n🔄 Customers examine products in 3D (rotate, zoom, customize)\n🎨 Change colors and see products in different environments\n📈 **Business impact:** Reduces returns, increases confidence\n💰 **ROI:** Higher conversion rates than static images\n💻 `new THREE.OrbitControls()` // Smooth product interaction",
        explanation: "The beauty of Three.js is its versatility. Whether you're a data scientist wanting to visualize complex information, a game developer creating the next viral browser game, an architect showcasing designs, or an artist exploring new forms of expression, Three.js provides the foundation to turn your vision into reality.",
        codeExample: `// Data Visualization Example - 3D Bar Chart
const chartData = [2, 5, 3, 8, 1, 6, 4];
const bars = new THREE.Group();

chartData.forEach((height, index) => {
  const bar = new THREE.Mesh(
    new THREE.BoxGeometry(0.8, height, 0.8),
    new THREE.MeshBasicMaterial({
      color: new THREE.Color().setHSL(index / 7, 0.8, 0.6)
    })
  );
  bar.position.x = index - 3;
  bar.position.y = height / 2;
  bars.add(bar);
});

scene.add(bars);
camera.position.set(5, 5, 5);
camera.lookAt(0, 2, 0);

console.log('📊 Interactive 3D data visualization created!');`,
        nextStep: "Next: Learn where to find help and inspiration"
      },
      {
        id: "step3-community-resources",
        title: "Step 3: Your Learning Toolkit",
        type: "navigation",
        content: "The Three.js community is one of its greatest strengths! 🤝 When you get stuck (and you will!), these resources will save you hours of frustration.\n\n📚 **OFFICIAL EXAMPLES GALLERY:**\n🌐 **Location:** threejs.org/examples\n✨ **What it offers:** 100+ working demonstrations with full source code\n🎯 **Why it's amazing:** Each example showcases specific techniques\n🔍 **How to use:** View source code, modify it, see results immediately\n💡 **Like having:** A master craftsperson show you techniques step by step\n💻 `// Click 'view source' on any example to learn`\n\n📖 **COMPREHENSIVE DOCUMENTATION:**\n🌐 **Location:** threejs.org/docs\n📋 **Coverage:** Every class, method, and property documented\n🎓 **Includes:** Not just what functions do, but when and why to use them\n🔄 **Always updated:** New features added constantly\n🔍 **Search tip:** Use Ctrl+F to find specific functions quickly\n💻 `// Example: search for 'BoxGeometry' to learn about cubes`\n\n💬 **COMMUNITY FORUM:**\n🌐 **Location:** discourse.threejs.org\n👥 **Who helps:** Developers helping each other solve problems\n🏆 **Special feature:** Three.js team actively participates\n🔍 **Pro tip:** Search before posting - someone likely solved your problem\n💡 **Best for:** Getting unstuck on specific issues\n💻 `// Post code snippets to get targeted help`\n\n🔧 **GITHUB REPOSITORY:**\n🌐 **Location:** github.com/mrdoob/three.js\n👀 **See:** Latest developments and future features\n🐛 **Report:** Bugs and request features\n💎 **Hidden gem:** Issue tracker full of advanced solutions\n🤝 **Contribute:** Help improve the library\n💻 `// Star the repo to stay updated`\n\n🎓 **THIRD-PARTY LEARNING:**\n📺 **Bruno Simon's Three.js Journey:** Premium course (highly recommended)\n🎬 **YouTube tutorials:** Free video lessons\n✏️ **CodePen experiments:** Interactive code examples\n📝 **Developer blogs:** Real-world tips and discoveries\n💻 `// Search 'Three.js tutorial' for endless learning content`",
        explanation: "Think of these resources as your personal advisory board. The examples gallery is your inspiration source, the documentation is your reference manual, the forum is your help desk, and GitHub is your window into the future of Three.js. Bookmark them all - you'll use them constantly.",
        codeExample: `// Essential bookmarks for your Three.js journey:

// 1. Examples Gallery (your inspiration source)
// https://threejs.org/examples/
// Over 100 working demos with source code

// 2. Documentation (your reference guide)
// https://threejs.org/docs/
// Complete API reference with explanations

// 3. Community Forum (your help desk)
// https://discourse.threejs.org/
// Ask questions and share your creations

// Let's create something inspired by the examples:
const inspirationCube = new THREE.Mesh(
  new THREE.BoxGeometry(1, 1, 1),
  new THREE.MeshBasicMaterial({
    color: 0xff6b6b,
    wireframe: true
  })
);
scene.add(inspirationCube);

// Animate it like many examples do
animate(() => {
  inspirationCube.rotation.x += 0.01;
  inspirationCube.rotation.y += 0.01;
});

console.log('🔗 Bookmark these resources - you will use them constantly!');`,
        nextStep: "Next: Discover common patterns in Three.js apps"
      },
      {
        id: "step4-common-patterns",
        title: "Step 4: The Three.js Recipe",
        type: "activity",
        content: "Every Three.js application follows the same fundamental pattern! 🍳 Understanding this pattern is like learning to cook - once you know the basic recipe, you can create anything.\n\n🎬 **STEP 1: SCENE SETUP (The Movie Set)**\n🌍 **Scene:** The 3D world where everything exists\n📷 **Camera:** Your viewpoint into the world\n🎨 **Renderer:** The artist that draws everything\n💡 **Like:** Setting up a movie set with actors, cameras, lighting\n💻 `const scene = new THREE.Scene()`\n💻 `const camera = new THREE.PerspectiveCamera()`\n💻 `const renderer = new THREE.WebGLRenderer()`\n\n🧱 **STEP 2: OBJECT CREATION (The Magic Formula)**\n📐 **Geometry:** Shape (cube, sphere, custom)\n🎨 **Material:** Appearance (color, texture, shininess)\n🎯 **Mesh:** Geometry + Material = Visible Object\n📍 **Positioning:** Place objects in 3D space using coordinates\n💻 `const mesh = new THREE.Mesh(geometry, material)`\n💻 `scene.add(mesh)`\n\n📷 **STEP 3: CAMERA POSITIONING (The Director's Choice)**\n🎬 Like a film director choosing camera angles\n👁️ Determines what the viewer sees\n📐 Position and rotation control the viewpoint\n🎯 Can be static or move dynamically\n💻 `camera.position.set(0, 0, 5)`\n💻 `camera.lookAt(0, 0, 0)`\n\n🔄 **STEP 4: ANIMATION LOOP (Bringing Life)**\n⚡ Runs 60 times per second\n📝 Updates object properties (position, rotation, scale, color)\n🎨 Redraws the scene each frame\n✨ Small changes create smooth motion illusion\n💻 `function animate() { requestAnimationFrame(animate); renderer.render(scene, camera); }`\n\n🎮 **STEP 5: EVENT HANDLING (Interactivity)**\n🖱️ Detect mouse clicks, keyboard presses, touch gestures\n🔄 Respond by changing object properties or camera position\n🎯 Makes scenes interactive and engaging\n💻 `window.addEventListener('click', onMouseClick)`\n\n🏆 **UNIVERSAL PATTERN:**\nThis appears in EVERY Three.js project:\n🎮 Simple spinning cube → Complex VR experience\n💡 **Key insight:** Complexity comes from what you do within this framework, not the framework itself",
        explanation: "Think of this pattern like the basic structure of a recipe: gather ingredients (create objects), prepare your workspace (set up scene and camera), combine ingredients (position objects), cook (animate), and serve (render). Whether you're making a simple sandwich or a gourmet meal, the basic process is the same.",
        codeExample: `// The Universal Three.js Recipe:

// 1. CREATE OBJECTS (Geometry + Material = Mesh)
const sphereGeometry = new THREE.SphereGeometry(1, 32, 32);
const sphereMaterial = new THREE.MeshBasicMaterial({ color: 0x4ecdc4 });
const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);

// 2. POSITION OBJECTS IN SPACE
sphere.position.set(2, 0, 0);
scene.add(sphere);

// 3. POSITION CAMERA FOR GOOD VIEW
camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

// 4. ADD ANIMATION (Optional but fun!)
animate(() => {
  sphere.rotation.y += 0.02;
  sphere.position.y = Math.sin(Date.now() * 0.003) * 0.5;
});

// This pattern appears in EVERY Three.js project!
// Games, data viz, art - they all follow this recipe

console.log('🍳 You now know the universal Three.js recipe!');`,
        nextStep: "🎉 You're ready to start your Three.js journey!"
      }
    ],
    initialCode: `// 🌟 Three.js Demo - See the Power of WebGL Abstraction!

// This simple demo showcases what makes Three.js special
// Notice how easy it is to create 3D graphics!

// Create a 3D object with just a few lines
const geometry = new THREE.BoxGeometry(1, 1, 1);
const material = new THREE.MeshBasicMaterial({
    color: 0x00ff88,
    wireframe: false
});
const cube = new THREE.Mesh(geometry, material);
scene.add(cube);

// Position camera to see our demo
camera.position.z = 3;

// Animation loop - smooth 60fps motion
animate(() => {
    // Rotate the cube
    cube.rotation.x += 0.01;
    cube.rotation.y += 0.01;

    // Dynamic color animation
    const time = Date.now() * 0.001;
    cube.material.color.setHSL(time * 0.1, 1, 0.5);
});

console.log('🎉 Three.js makes 3D graphics accessible!');
console.log('📚 Explore threejs.org/examples for inspiration');
console.log('🛠️ Check threejs.org/docs for complete API reference');

// What this simple code demonstrates:
// ✨ Easy 3D object creation (geometry + material = mesh)
// 🎨 Dynamic material properties and animations
// 🔄 Smooth browser-optimized rendering
// 📱 Cross-browser WebGL compatibility

// Compare this to raw WebGL:
// - No manual vertex buffer management
// - No shader compilation complexity
// - No matrix math calculations
// - No browser compatibility worries

// Try experimenting:
// - Change wireframe to true: material.wireframe = true
// - Try different shapes: SphereGeometry, CylinderGeometry
// - Modify colors: color: 0xff0000 (red), 0x0000ff (blue)`,
    tasks: [
      {
        title: "🎯 Create Your First Shape",
        description: "Modify the demo to create a different 3D shape and change its color",
        hint: "Try SphereGeometry or CylinderGeometry instead of BoxGeometry, and experiment with different colors"
      },
      {
        title: "🌐 Explore the Examples Gallery",
        description: "Visit threejs.org/examples and find one demo that interests you. Try to identify the basic Three.js pattern in its code",
        hint: "Look for scene creation, object creation, camera positioning, and animation loops"
      },
      {
        title: "🔧 Experiment with Animation",
        description: "Make your 3D object move in a different way - try making it bounce, scale, or change colors",
        hint: "Use Math.sin() for bouncing motion, or try animating the scale property"
      }
    ],
    examples: [
      {
        title: "Three.js Application Categories",
        description: "Major categories of Three.js applications with examples",
        code: `// 1. DATA VISUALIZATION
// - Interactive 3D charts and graphs
// - Network diagrams and node relationships
// - Scientific data representation
// Example: COVID-19 spread visualization, stock market 3D charts

// 2. GAMING & ENTERTAINMENT
// - Browser-based 3D games
// - Interactive experiences and simulations
// - Virtual reality applications
// Example: Racing games, puzzle games, VR experiences

// 3. ARCHITECTURE & DESIGN
// - Virtual building walkthroughs
// - Interior design visualization
// - Urban planning models
// Example: Real estate tours, architectural presentations

// 4. PRODUCT SHOWCASES
// - Interactive 3D product viewers
// - Configurators and customization tools
// - E-commerce 3D previews
// Example: Car configurators, furniture viewers, jewelry displays

// 5. ART & CREATIVE
// - Interactive art installations
// - Music visualizers and audio-reactive graphics
// - Generative art and creative coding
// Example: Museum installations, concert visuals, digital art`
      },
      {
        title: "Three.js vs Raw WebGL Comparison",
        description: "Understanding the abstraction benefits",
        code: `// RAW WEBGL (Complex, hundreds of lines needed)
// - Manual vertex buffer creation
// - Shader compilation and linking
// - Matrix calculations by hand
// - Cross-browser compatibility issues

// THREE.JS (Simple, intuitive)
const geometry = new THREE.BoxGeometry(1, 1, 1);
const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });
const cube = new THREE.Mesh(geometry, material);
scene.add(cube);

// BENEFITS OF THREE.JS:
// ✅ Rapid development and prototyping
// ✅ Extensive documentation and examples
// ✅ Large community and ecosystem
// ✅ Regular updates and maintenance
// ✅ Built-in optimizations and best practices
// ✅ Cross-browser compatibility handled automatically

// WHEN TO CONSIDER ALTERNATIVES:
// 🤔 Extremely performance-critical applications
// 🤔 Very specific rendering requirements
// 🤔 Existing game engine workflows
// 🤔 Mobile-first applications with strict size constraints`
      }
    ]
  },

  2: {
    id: 2,
    title: "Scene, Camera & Renderer Concepts",
    difficulty: "Beginner",
    category: "Core Concepts",
    description: "Master the fundamental trio that powers every Three.js application. Learn how scene, camera, and renderer collaborate to produce 3D visuals.",
    objectives: [
      "Define the roles of scene, camera, and renderer in 3D workflows",
      "Illustrate how these components collaborate to produce visuals",
      "Conceptualize the continuous rendering process",
      "Plan the data flow from objects to screen"
    ],
    theory: [
      "Scene graph architecture creates hierarchical relationships of objects where parent transformations affect children, enabling complex animations and groupings.",
      "Camera projections determine how 3D space is flattened to 2D. Perspective cameras mimic human vision with depth, while orthographic cameras maintain consistent scale.",
      "The renderer's role is transforming scene data into pixels on the canvas, handling complex calculations like lighting and materials behind the scenes.",
      "The animation loop uses browser-driven updates with requestAnimationFrame, ensuring smooth rendering while being efficient with system resources."
    ],
    concepts: [
      {
        name: "Scene Graph Architecture",
        description: "Hierarchical relationship of objects in 3D space",
        example: "Parent transformations affect children - move a group, move all contents"
      },
      {
        name: "Camera Projections",
        description: "Perspective vs orthographic visual differences",
        example: "Perspective: realistic depth; Orthographic: consistent scale"
      },
      {
        name: "Renderer Role",
        description: "From scene data to pixels on the canvas",
        example: "Handles WebGL complexity, lighting calculations, material rendering"
      },
      {
        name: "Animation Loop",
        description: "Browser-driven updates using frame callbacks",
        example: "requestAnimationFrame ensures smooth 60fps rendering"
      }
    ],
    learningPath: [
      {
        id: "step1-scene-stage",
        title: "Step 1: The Scene is Your Stage",
        type: "concept",
        content: "The **Scene** is your 3D world where everything exists. 🎭 Think of it as an infinite theater stage extending in all directions.\n\n🌍 **3D COORDINATE SYSTEM:**\n📍 Center at origin (0, 0, 0)\n➡️ **X-axis:** left/right\n⬆️ **Y-axis:** up/down  \n⬅️ **Z-axis:** forward/backward\n\n🏗️ **SCENE GRAPH HIERARCHY:**\n👨‍👩‍👧‍👦 Objects can be grouped (parent-child relationships)\n🌞 Move parent = children move automatically\n🪐 Example: Solar system - move sun, planets follow\n\n💻 `const scene = new THREE.Scene()`\n💻 `scene.add(myObject)` // Makes object visible\n\n🎨 **GLOBAL PROPERTIES:**\n🎨 Background color, fog effects\n🌅 Environment lighting, panoramic images\n🌙 Procedural skies (day/night cycles)\n\n⚠️ **IMPORTANT:** Objects must be added with scene.add() to be visible!",
        explanation: "Think of the scene as the master container for your 3D world. Just like a theater director needs a stage to place actors and props, you need a scene to place your 3D objects. The scene keeps track of everything, organizes relationships between objects, and provides the context for rendering.",
        codeExample: `// The Scene is like a theater stage
scene.background = new THREE.Color(0x87CEEB); // Set a sky blue backdrop

// Create some "actors" for our stage
const actor1 = new THREE.Mesh(
  new THREE.BoxGeometry(1, 1, 1),
  new THREE.MeshBasicMaterial({ color: 0xff6b6b })
);
actor1.position.x = -2;

const actor2 = new THREE.Mesh(
  new THREE.SphereGeometry(0.7, 32, 32),
  new THREE.MeshBasicMaterial({ color: 0x4ecdc4 })
);
actor2.position.x = 2;

// Add actors to the stage
scene.add(actor1);
scene.add(actor2);

console.log('🎭 Scene now contains', scene.children.length, 'objects');
console.log('The scene is your 3D world container!');`,
        nextStep: "Next: Position your camera to see the action"
      },
      {
        id: "step2-camera-viewpoint",
        title: "Step 2: Camera is Your Viewpoint",
        type: "concept",
        content: "The **Camera** is your window into the 3D world. 📷 It determines what appears on screen and how it looks.\n\n📹 **CAMERA TYPES:**\n\n🎯 **PerspectiveCamera** - Realistic human vision\n👁️ Objects farther away appear smaller\n🎮 Perfect for: games, walkthroughs, realistic scenes\n💻 `new THREE.PerspectiveCamera(75, width/height, 0.1, 1000)` // FOV, aspect ratio, near, far\n\n📐 **OrthographicCamera** - Technical view\n📏 Objects same size regardless of distance\n🏗️ Perfect for: technical drawings, isometric games\n\n🎛️ **CAMERA CONTROLS:**\n📍 **Position:** (x, y, z) coordinates in 3D space\n🔄 **Rotation:** Which direction camera faces\n👀 **LookAt:** Automatically face a point\n💻 `camera.position.set(0, 5, 10)`\n💻 `camera.lookAt(0, 0, 0)` // Look at origin\n\n🔍 **FIELD OF VIEW (FOV):**\n📐 45-75° = normal view\n🔭 Narrow FOV = telephoto (zoomed in)\n🐟 Wide FOV = fisheye (wide angle)\n\n✂️ **CLIPPING PLANES:**\n📏 **Near plane:** Too close = not rendered\n🌌 **Far plane:** Too far = not rendered\n⚡ Optimization: don't waste power on invisible objects",
        explanation: "Think of the camera as your eyes in the 3D world. Just like you can walk around a sculpture in a museum to see it from different angles, you can move the camera to explore your 3D scene. The camera's position and settings dramatically affect how your scene feels - close and intimate, distant and grand, or anything in between.",
        codeExample: `// Position the camera like a movie director
camera.position.set(0, 3, 6); // Move camera up and back
camera.lookAt(0, 0, 0);       // Point camera at center of scene

// Create objects to demonstrate different camera views
const redCube = new THREE.Mesh(
  new THREE.BoxGeometry(1, 1, 1),
  new THREE.MeshBasicMaterial({ color: 0xff0000 })
);
redCube.position.x = -1;

const blueSphere = new THREE.Mesh(
  new THREE.SphereGeometry(0.5, 32, 32),
  new THREE.MeshBasicMaterial({ color: 0x0000ff })
);
blueSphere.position.x = 1;

scene.add(redCube, blueSphere);

// Try different camera positions:
// camera.position.set(5, 0, 0);  // Side view
// camera.position.set(0, 5, 0);  // Top view
// camera.position.set(0, 0, 5);  // Front view

console.log('📷 Camera positioned at:', camera.position);
console.log('Camera is looking at the center of your scene');`,
        nextStep: "Next: Understand how the renderer draws everything"
      },
      {
        id: "step3-renderer-artist",
        title: "Step 3: Renderer is Your Artist",
        type: "concept",
        content: "The **Renderer** is the powerhouse that transforms your 3D scene into the 2D image you see on screen. 🎨 It's like having a master artist who can paint photorealistic images **60 times per second**!\n\n🔄 **THE RENDERING PROCESS:**\n🔹 **Step 1:** Transform 3D coordinates into 2D screen coordinates\n🔹 **Step 2:** Calculate which objects are visible from the camera\n🔹 **Step 3:** Determine depth of every pixel to handle overlapping objects\n\n💡 **LIGHTING CALCULATIONS:**\n✨ Computes how **light bounces off surfaces** (highlights, shadows, reflections)\n✨ Considers **material properties** (shininess, transparency, color)\n\n⚡ **WHY IT'S SO FAST:**\n🚀 Uses your computer's **graphics card (GPU)** for maximum performance\n🚀 **Parallel processing** calculates thousands of pixels simultaneously\n🚀 This enables smooth 3D graphics even with complex scenes\n\n⚖️ **QUALITY VS PERFORMANCE:**\n📈 **Antialiasing** → smoother edges, more processing power\n📈 **Shadow mapping** → realistic shadows, computational overhead\n📈 **Auto-optimization** → frustum culling, backface culling\n\n🎯 **COOL FEATURES:**\n🌟 Transparency, blending, and post-processing effects\n🌟 Manages render loop with browser's animation system",
        explanation: "Think of the renderer as a master photographer with a magical camera. It can instantly capture a perfect image of your 3D world from any angle, adjusting lighting, focus, and exposure automatically. The magic is that it does this 60 times per second, creating the illusion of smooth motion and real-time interaction.",
        codeExample: `// The renderer works behind the scenes automatically
// But let's see what it's doing conceptually:

// Create a simple scene to render
const geometry = new THREE.BoxGeometry(1, 1, 1);
const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
const cube = new THREE.Mesh(geometry, material);
scene.add(cube);

// Position camera
camera.position.z = 3;

// The renderer automatically:
// 1. Takes the 3D scene data
// 2. Applies camera transformation
// 3. Projects 3D coordinates to 2D screen
// 4. Fills in pixels with the right colors
// 5. Displays the final image

// This happens 60 times per second!
console.log('🎨 Renderer converts 3D scene → 2D pixels on screen');
console.log('It handles all the complex WebGL operations for you');

// The magic: renderer.render(scene, camera)
// This one line creates your entire 3D image!`,
        nextStep: "Next: Learn how animation brings everything to life"
      },
      {
        id: "step4-animation-loop",
        title: "Step 4: Animation Loop Brings Life",
        type: "concept",
        content: "The **Animation Loop** is the heartbeat ❤️ of every 3D application. It updates object properties and redraws the scene **60 times per second**, creating smooth motion! ✨\n\n🔄 **HOW IT WORKS:**\n**1.** 📝 Update object properties (position, rotation, scale, color)\n**2.** 🎨 Render the scene with new properties\n**3.** 🔁 Repeat **60 times per second**\n**4.** 🧠 Your brain sees smooth motion (like movies with 24 frames per second)\n\n⏰ **REQUESTANIMATIONFRAME:**\n🎯 Browser's **built-in timing** syncs with display refresh rate\n🎯 Ensures **smooth animation** and prevents screen tearing\n🎯 **Auto-pauses** when tab isn't visible (saves battery)\n\n💻 `animate(() => { cube.rotation.y += 0.01; })` // Update objects here\n\n⚡ **DELTA TIME FOR CONSISTENCY:**\n🎮 Multiply movement by **time elapsed** (not fixed amounts)\n🎮 Ensures **same speed** regardless of frame rate\n🎮 60fps and 120fps displays show **identical motion**\n\n🎮 **WHERE INTERACTIVITY HAPPENS:**\n🖱️ **User Input** → mouse clicks, keyboard presses\n🌍 **Physics** → gravity, collisions, realistic movement\n🎨 **Materials** → changing colors, transparency, glowing\n🎭 **Events** → complex choreographed sequences\n\n🚀 **OPTIMIZATION TIPS:**\n💾 Minimize expensive calculations or cache results\n♻️ Use object pooling (reuse vs create new)\n📏 Level-of-detail systems (simpler distant objects)\n🎯 **Goal:** maintain **60fps**",
        explanation: "Think of the animation loop as the conductor of an orchestra. Every 1/60th of a second, it cues each musician (object) to play their next note (update their properties), then the entire orchestra (scene) performs together (renders) to create a harmonious performance (smooth animation).",
        codeExample: `// Create objects that will move
const spinningCube = new THREE.Mesh(
  new THREE.BoxGeometry(1, 1, 1),
  new THREE.MeshBasicMaterial({ color: 0xff6b6b })
);
spinningCube.position.x = -2;

const bouncingBall = new THREE.Mesh(
  new THREE.SphereGeometry(0.5, 32, 32),
  new THREE.MeshBasicMaterial({ color: 0x4ecdc4 })
);
bouncingBall.position.x = 2;

scene.add(spinningCube, bouncingBall);

// Position camera to see the action
camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

// The animation loop - this runs 60 times per second!
animate(() => {
  // Make tiny changes each frame
  spinningCube.rotation.y += 0.02;  // Spin the cube

  // Use math for smooth bouncing motion
  const time = Date.now() * 0.003;
  bouncingBall.position.y = Math.sin(time) * 1.5;

  // The renderer automatically redraws everything!
});

console.log('🎬 Animation loop running at 60 FPS!');
console.log('Small changes each frame = smooth motion');`,
        nextStep: "🎉 You understand the Three.js pipeline!"
      }
    ],
    initialCode: `// 🎬 Scene, Camera & Renderer - The Three.js Trinity

// This demo shows how the three core components work together
// to create 3D graphics from data to pixels on screen

// 1. SCENE - The 3D world container (already created!)
scene.background = new THREE.Color(0x222222);
console.log('📍 Scene: 3D world container ready');

// 2. CAMERA - Our viewpoint into the 3D world (already created!)
camera.position.set(5, 3, 5);  // Position camera in 3D space
camera.lookAt(0, 0, 0);        // Point camera at scene center
console.log('📷 Camera: Viewpoint positioned and aimed');

// 3. RENDERER - Converts 3D scene to 2D pixels (already created!)
console.log('🎨 Renderer: Ready to draw 60fps');

// Create objects to demonstrate the pipeline
const cubeGeometry = new THREE.BoxGeometry(1, 1, 1);
const sphereGeometry = new THREE.SphereGeometry(0.7, 32, 32);

// Demonstrate scene graph hierarchy
const group = new THREE.Group(); // Parent container
scene.add(group);

const redCube = new THREE.Mesh(cubeGeometry,
    new THREE.MeshBasicMaterial({ color: 0xff6b6b }));
redCube.position.x = -2;
group.add(redCube); // Child of group

const blueSphere = new THREE.Mesh(sphereGeometry,
    new THREE.MeshBasicMaterial({ color: 0x4ecdc4 }));
blueSphere.position.x = 2;
group.add(blueSphere); // Child of group

console.log('🏗️ Scene graph: Group contains', group.children.length, 'objects');

// Animation loop - the continuous rendering cycle
animate(() => {
    // Update individual objects
    redCube.rotation.y += 0.02;
    blueSphere.rotation.x += 0.01;

    // Rotate the entire group (affects all children!)
    group.rotation.y += 0.005;

    // The render pipeline: Scene → Camera → Pixels
    // This happens automatically 60 times per second!
});

// 🚀 Experiment with the pipeline:
// - Move camera: camera.position.set(0, 10, 0)
// - Change background: scene.background = new THREE.Color(0x87CEEB)
// - Add objects to group: group.add(newObject)
// - Try different camera targets: camera.lookAt(2, 0, 0)`,
    tasks: [
      {
        title: "🎭 Build a Solar System",
        description: "Create a simple solar system with a sun in the center and a planet that orbits around it",
        hint: "Use THREE.Group() to create an orbit container, add the planet to it, then rotate the group"
      },
      {
        title: "📷 Camera Movement",
        description: "Try moving the camera to different positions and see how it changes your view of the scene",
        hint: "Experiment with camera.position.set(x, y, z) and camera.lookAt(0, 0, 0)"
      },
      {
        title: "🎬 Create Multiple Animations",
        description: "Make different objects move in different ways - one spinning, one bouncing, one changing size",
        hint: "Use rotation for spinning, Math.sin() for bouncing, and scale for size changes"
      }
    ],
    examples: [
      {
        title: "Scene Graph Hierarchy Patterns",
        description: "Common patterns for organizing 3D objects",
        code: `// Pattern 1: Solar System (Nested Rotations)
const solarSystem = new THREE.Group();
const earthOrbit = new THREE.Group();
const moonOrbit = new THREE.Group();

solarSystem.add(sun);
solarSystem.add(earthOrbit);
earthOrbit.add(earth);
earthOrbit.add(moonOrbit);
moonOrbit.add(moon);

// Pattern 2: Character Hierarchy (Body Parts)
const character = new THREE.Group();
const torso = new THREE.Group();
const leftArm = new THREE.Group();
const rightArm = new THREE.Group();

character.add(torso);
torso.add(leftArm);
torso.add(rightArm);

// Pattern 3: Building Structure (Floors and Rooms)
const building = new THREE.Group();
const floor1 = new THREE.Group();
const floor2 = new THREE.Group();

building.add(floor1);
building.add(floor2);
floor1.add(room1, room2, room3);
floor2.add(room4, room5, room6);`
      },
      {
        title: "Camera Types and Use Cases",
        description: "When to use different camera projections",
        code: `// PERSPECTIVE CAMERA - Realistic depth
// Use for: Games, architectural walkthroughs, realistic scenes
const perspectiveCamera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000);

// ORTHOGRAPHIC CAMERA - No perspective distortion
// Use for: Technical drawings, UI overlays, isometric games
const orthoCamera = new THREE.OrthographicCamera(-10, 10, 10, -10, 0.1, 1000);

// Camera Controls and Movement
camera.position.set(x, y, z);     // Position in 3D space
camera.lookAt(targetX, targetY, targetZ); // What to look at
camera.up.set(0, 1, 0);          // Which way is "up"

// Field of View Effects
camera.fov = 45;  // Narrow (telephoto lens effect)
camera.fov = 120; // Wide (fisheye lens effect)
camera.updateProjectionMatrix(); // Apply changes

// Camera Animation
function orbitCamera(time) {
  const radius = 10;
  camera.position.x = Math.cos(time) * radius;
  camera.position.z = Math.sin(time) * radius;
  camera.lookAt(0, 0, 0);
}`
      },
      {
        title: "Render Pipeline Optimization",
        description: "Understanding performance implications",
        code: `// Renderer Configuration for Performance
const renderer = new THREE.WebGLRenderer({
  antialias: false,        // Disable for better performance
  powerPreference: "high-performance",
  stencil: false,         // Disable if not needed
  depth: true             // Usually needed for 3D
});

// Performance Monitoring
let frameCount = 0;
let lastTime = performance.now();

function measurePerformance() {
  frameCount++;
  const currentTime = performance.now();

  if (currentTime - lastTime >= 1000) {
    const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
    console.log('FPS:', fps);
    frameCount = 0;
    lastTime = currentTime;
  }
}

// Render Loop with Performance Monitoring
function animate() {
  requestAnimationFrame(animate);
  measurePerformance();

  // Update scene
  updateObjects();

  // Render: Scene + Camera = Pixels
  renderer.render(scene, camera);
}`
      }
    ]
  },

  3: {
    id: 3,
    title: "Geometry & Material Theory",
    difficulty: "Beginner",
    category: "Core Concepts",
    description: "Explore the building blocks of 3D objects. Learn about geometric primitives, material types, and how they combine to create meshes.",
    objectives: [
      "Recognize built-in geometric primitives and their properties",
      "Explain different shading models and material types",
      "Comprehend the mesh concept as geometry + material",
      "Understand how normals impact shading and appearance"
    ],
    theory: [
      "Primitive geometries like boxes, spheres, and planes are the foundation of 3D modeling. Each has parameters that control shape, detail level, and performance.",
      "Material varieties range from Basic (unlit) for UI elements, to Lambert (diffuse) for matte surfaces, Phong (specular) for shiny objects, and Standard (PBR) for photorealistic rendering.",
      "Wireframe vs solid rendering serves different purposes: wireframe is excellent for debugging geometry and creating stylistic effects, while solid rendering provides realistic appearance.",
      "Surface normals are invisible vectors that point outward from each face, determining how light interacts with the surface and creating smooth or faceted shading."
    ],
    concepts: [
      {
        name: "Primitive Geometries",
        description: "Box, sphere, plane: parameter implications",
        example: "BoxGeometry(width, height, depth, segments) - more segments = smoother curves"
      },
      {
        name: "Material Varieties",
        description: "Basic (unlit), Lambert (diffuse), Phong (specular), Standard (PBR)",
        example: "MeshBasicMaterial ignores lights, MeshStandardMaterial responds to lighting"
      },
      {
        name: "Wireframe vs Solid Rendering",
        description: "Debugging vs stylistic decisions",
        example: "wireframe: true shows geometry structure, false shows solid surfaces"
      },
      {
        name: "Normals & Shading",
        description: "Role of surface normals in light calculations",
        example: "Normals determine how light bounces off surfaces for realistic shading"
      }
    ],
    learningPath: [
      {
        id: "step1-basic-shapes",
        title: "Step 1: Basic 3D Shapes",
        type: "concept",
        content: "**Geometry** defines the shape and structure of 3D objects. 🏗️ Think of it as the wireframe skeleton - like a house frame before walls are added.\n\n🎯 **BUILT-IN SHAPES:**\n\n📦 **BOXGEOMETRY** - Rectangular shapes\n🏢 Perfect for: buildings, containers, dice\n⚙️ Control: width, height, depth, segments\n💻 `new THREE.BoxGeometry(1, 1, 1)` // width, height, depth\n\n🌍 **SPHEREGEOMETRY** - Round shapes\n⚽ Perfect for: balls, planets, bubbles\n🎛️ Control: radius, width/height segments\n🪩 8 segments = disco ball, 32 segments = smooth\n💻 `new THREE.SphereGeometry(1, 32, 16)` // radius, widthSeg, heightSeg\n\n🏛️ **CYLINDERGEOMETRY** - Tube shapes\n🌳 Perfect for: columns, trees, bottles\n🔺 Different radii = cones\n💻 `new THREE.CylinderGeometry(0.5, 0.5, 2)` // topRadius, bottomRadius, height\n\n📱 **PLANEGEOMETRY** - Flat surfaces\n🏠 Perfect for: walls, floors, screens\n🌊 More subdivisions = wave deformations\n💻 `new THREE.PlaneGeometry(2, 2)` // width, height\n\n⚖️ **PERFORMANCE BALANCE:**\n📈 More segments = smoother shapes, more processing power\n🔍 Close objects need detail, distant objects can be simple",
        explanation: "Think of geometries as the fundamental building blocks of 3D creation, like LEGO pieces. Each piece has a specific shape and purpose, but you can combine them in infinite ways to create anything you can imagine. The art is knowing which pieces to use and how to combine them effectively.",
        codeExample: `// Create different basic shapes
const box = new THREE.Mesh(
  new THREE.BoxGeometry(1, 1, 1),
  new THREE.MeshBasicMaterial({ color: 0xff6b6b })
);
box.position.x = -2;

const sphere = new THREE.Mesh(
  new THREE.SphereGeometry(0.7, 32, 16),
  new THREE.MeshBasicMaterial({ color: 0x4ecdc4 })
);
sphere.position.x = 0;

const cylinder = new THREE.Mesh(
  new THREE.CylinderGeometry(0.5, 0.5, 1, 8),
  new THREE.MeshBasicMaterial({ color: 0xf39c12 })
);
cylinder.position.x = 2;

scene.add(box, sphere, cylinder);
camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

console.log('🔧 Three basic shapes created!');`,
        nextStep: "Next: Learn about materials and colors"
      },
      {
        id: "step2-materials-colors",
        title: "Step 2: Materials and Colors",
        type: "concept",
        content: "**Materials** define how your 3D objects look - color, texture, shininess, and light interaction. 🎨 Geometry gives **shape**, materials give **personality**.\n\n🎭 **MATERIAL TYPES:**\n\n🟦 **MESHBASICMATERIAL** - Simplest option\n🎯 Solid colors, ignores lighting completely\n📱 Flat, uniform appearance\n⚡ Most performance-friendly\n💻 `new THREE.MeshBasicMaterial({ color: 0xff0000 })`\n\n📄 **MESHLAMBERTMATERIAL** - Matte surfaces\n💡 Responds to lighting, realistic matte look\n🎨 Like chalk, paper, unpolished wood\n🔆 Brighter facing light, darker facing away\n💻 `new THREE.MeshLambertMaterial({ color: 0x00ff00 })`\n\n✨ **MESHPHONGMATERIAL** - Shiny surfaces\n💎 Adds specular highlights\n🏭 Like plastic, metal, polished surfaces\n🎚️ Control shininess: glossy to mirror-like\n💻 `new THREE.MeshPhongMaterial({ color: 0x0000ff, shininess: 100 })`\n\n🏆 **MESHSTANDARDMATERIAL** - Most realistic\n🔬 Physically-based rendering (PBR)\n⚙️ Uses metalness and roughness parameters\n🎯 Looks convincing under any lighting\n💻 `new THREE.MeshStandardMaterial({ color: 0xffffff, metalness: 0.5, roughness: 0.3 })`\n\n🎨 **COLOR OPTIONS:**\n🔢 Hex: `0xff0000` (red) | 🎛️ RGB values | 🌈 HSL mixing | 🖼️ Textures",
        explanation: "Think of materials as the skin, paint, or clothing for your 3D objects. Just like how different fabrics (silk, denim, leather) look and feel different under the same lighting, different material types create vastly different visual impressions even when applied to identical geometry.",
        codeExample: `// Create the same shape with different materials
const geometry = new THREE.SphereGeometry(0.8, 32, 16);

// Basic Material - simple color, no lighting effects
const basicSphere = new THREE.Mesh(
  geometry,
  new THREE.MeshBasicMaterial({ color: 0xff0000 })
);
basicSphere.position.x = -2;

// Lambert Material - responds to light, matte finish
const lambertSphere = new THREE.Mesh(
  geometry,
  new THREE.MeshLambertMaterial({ color: 0x00ff00 })
);
lambertSphere.position.x = 0;

// Phong Material - shiny, reflects light
const phongSphere = new THREE.Mesh(
  geometry,
  new THREE.MeshPhongMaterial({ color: 0x0000ff, shininess: 100 })
);
phongSphere.position.x = 2;

scene.add(basicSphere, lambertSphere, phongSphere);
camera.position.set(0, 2, 6);
camera.lookAt(0, 0, 0);

console.log('🎨 Same shape, different materials!');`,
        nextStep: "Next: Discover wireframe mode"
      },
      {
        id: "step3-wireframe-mode",
        title: "Step 3: Wireframe Mode",
        type: "concept",
        content: "**Wireframe mode** shows only the edges of triangular faces, revealing the skeleton structure of 3D objects. 🔍\n\n🔺 **THE TRIANGLE TRUTH:**\n🎯 Every 3D object is built from **triangles**\n👁️ Wireframe reveals this hidden structure\n🌊 Curved surfaces = many small triangles\n🌍 Sphere = hundreds of triangles, Cube = 12 triangles\n\n🎨 **PRACTICAL USES:**\n🏗️ Architectural blueprints, technical drawings\n🕹️ Retro computer graphics aesthetic\n🚀 Sci-fi interfaces, holographic displays\n\n⚙️ **HOW TO ENABLE:**\n💻 `material.wireframe = true`\n🖊️ Draws only triangle edges (no solid fill)\n🎭 Combine with colors and transparency\n\n🔧 **DEBUGGING & OPTIMIZATION:**\n📊 Shows triangle count (affects performance)\n⚠️ Dense wireframes = too many triangles\n🐛 Reveals holes, overlapping faces, bad normals\n🔍 Problems invisible in solid rendering\n\n✨ **CREATIVE EFFECTS:**\n🎭 Layer wireframe + solid materials\n👻 Transparent solid + wireframe overlay = X-ray look\n🔬 Technical, futuristic appearance",
        explanation: "Think of wireframe mode as architectural blueprints for your 3D objects. Just as blueprints show the structural framework of a building before the walls and decorations are added, wireframe mode reveals the triangular framework that gives your 3D objects their shape.",
        codeExample: `// Compare solid vs wireframe rendering
const geometry = new THREE.SphereGeometry(1, 16, 8);

// Solid sphere - normal appearance
const solidSphere = new THREE.Mesh(
  geometry,
  new THREE.MeshBasicMaterial({ color: 0x4ecdc4 })
);
solidSphere.position.x = -1.5;

// Wireframe sphere - shows the structure
const wireframeSphere = new THREE.Mesh(
  geometry,
  new THREE.MeshBasicMaterial({
    color: 0xff6b6b,
    wireframe: true
  })
);
wireframeSphere.position.x = 1.5;

scene.add(solidSphere, wireframeSphere);
camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

// Animate to see the structure better
animate(() => {
  solidSphere.rotation.y += 0.01;
  wireframeSphere.rotation.y += 0.01;
});

console.log('🔍 Wireframe shows the 3D structure!');`,
        nextStep: "Next: Combine geometry and materials"
      },
      {
        id: "step4-geometry-plus-material",
        title: "Step 4: The Magic Formula",
        type: "concept",
        content: "The fundamental equation of 3D graphics:\n\n🧮 **GEOMETRY + MATERIAL = MESH** 🧮\n\nThis simple formula builds **everything** in 3D - from spinning cubes to complex virtual worlds! ✨\n\n🏗️ **GEOMETRY = STRUCTURE:**\n📐 Mathematical shape description\n📍 Vertices (corner points), edges, faces\n🌍 Defines where every point exists in 3D space\n⚠️ NO visual info (no color, texture, lighting)\n\n🎨 **MATERIAL = APPEARANCE:**\n✨ All visual properties\n🌈 Color, texture, shininess, transparency\n💡 How surface interacts with light\n🎭 Same geometry + different materials = totally different objects\n\n🎯 **MESH = FINAL OBJECT:**\n🔗 Combines geometry + material\n👁️ The actual 3D object in your scene\n📍 Has position, rotation, scale properties\n\n💻 **THE MAGIC FORMULA IN CODE:**\n💻 `const geometry = new THREE.BoxGeometry(1, 1, 1)`\n💻 `const material = new THREE.MeshBasicMaterial({ color: 0xff0000 })`\n💻 `const mesh = new THREE.Mesh(geometry, material)`\n💻 `scene.add(mesh)`\n\n💪 **WHY SEPARATION IS POWERFUL:**\n♻️ Reuse geometry with different materials\n🎨 Apply same material to different geometries\n💾 Saves memory and processing power\n\n🎯 **YOUR 3D CREATION PROCESS:**\n**1.** Choose geometry for shape\n**2.** Select material for appearance\n**3.** Combine into mesh\n**4.** Position in scene\n🚀 Scales from demos to complex apps!",
        explanation: "Think of this formula like making cookies. The geometry is your cookie cutter (defines the shape), the material is your icing and decorations (defines the appearance), and the mesh is the finished decorated cookie ready to serve (the final 3D object in your scene).",
        codeExample: `// The Magic Formula: Geometry + Material = Mesh

// Step 1: Choose a geometry (shape)
const cubeGeometry = new THREE.BoxGeometry(1, 1, 1);
const sphereGeometry = new THREE.SphereGeometry(0.7, 32, 16);

// Step 2: Choose materials (appearance)
const redMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });
const blueMaterial = new THREE.MeshBasicMaterial({ color: 0x0000ff });
const wireframeMaterial = new THREE.MeshBasicMaterial({
  color: 0x00ff00,
  wireframe: true
});

// Step 3: Combine them to create meshes
const redCube = new THREE.Mesh(cubeGeometry, redMaterial);
redCube.position.x = -2;

const blueSphere = new THREE.Mesh(sphereGeometry, blueMaterial);
blueSphere.position.x = 0;

const wireframeCube = new THREE.Mesh(cubeGeometry, wireframeMaterial);
wireframeCube.position.x = 2;

// Step 4: Add to scene to make them visible
scene.add(redCube, blueSphere, wireframeCube);

camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

console.log('✨ Geometry + Material = Mesh (the magic formula!)');`,
        nextStep: "🎉 You understand the building blocks of 3D objects!"
      }
    ],
    initialCode: `// 🔧 Geometry & Materials - Building Blocks of 3D Objects

// Geometries define SHAPE, Materials define APPEARANCE
// Geometry + Material = Mesh (visible 3D object)

// Create different geometries to explore
const boxGeo = new THREE.BoxGeometry(1, 1, 1);
const sphereGeo = new THREE.SphereGeometry(0.7, 32, 16);
const cylinderGeo = new THREE.CylinderGeometry(0.5, 0.5, 1, 8);

// Create different materials to compare
const basicMaterial = new THREE.MeshBasicMaterial({
  color: 0xff6b6b,
  wireframe: false
});

const wireframeMaterial = new THREE.MeshBasicMaterial({
  color: 0x4ecdc4,
  wireframe: true
});

// Combine geometry + material = mesh
const solidBox = new THREE.Mesh(boxGeo, basicMaterial);
solidBox.position.x = -2;

const wireframeSphere = new THREE.Mesh(sphereGeo, wireframeMaterial);
wireframeSphere.position.x = 0;

const solidCylinder = new THREE.Mesh(cylinderGeo, basicMaterial.clone());
solidCylinder.material.color.setHex(0xf39c12);
solidCylinder.position.x = 2;

scene.add(solidBox, wireframeSphere, solidCylinder);

// Position camera to see all objects
camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

// Animate to show 3D structure
animate(() => {
  solidBox.rotation.y += 0.01;
  wireframeSphere.rotation.x += 0.01;
  solidCylinder.rotation.z += 0.01;
});

console.log('🔧 Geometry defines shape, Material defines appearance');
console.log('🎨 Wireframe shows geometry structure, solid shows final appearance');

// Try experimenting:
// - Change wireframe: material.wireframe = true/false
// - Adjust geometry segments: new THREE.SphereGeometry(0.7, 8, 4) vs (0.7, 32, 16)
// - Try different colors: material.color.setHex(0x00ff00)`,
    tasks: [
      {
        title: "🎯 Create a Robot",
        description: "Use different geometries to build a simple robot - boxes for the body, spheres for joints, cylinders for arms",
        hint: "Position different shapes using .position.x, .position.y, .position.z to build your robot"
      },
      {
        title: "🌈 Material Playground",
        description: "Create the same shape with 3 different materials and see how they look different",
        hint: "Try MeshBasicMaterial, MeshLambertMaterial, and set wireframe: true on one of them"
      },
      {
        title: "🔍 Wireframe Detective",
        description: "Turn on wireframe mode for different shapes and count how many triangles make up each one",
        hint: "Set wireframe: true in the material and observe how spheres, boxes, and cylinders are constructed"
      }
    ],
    examples: [
      {
        title: "Geometry Parameter Effects",
        description: "How parameters affect geometry appearance and performance",
        code: `// Low detail (better performance)
const lowDetailSphere = new THREE.SphereGeometry(1, 8, 4);

// High detail (better appearance)
const highDetailSphere = new THREE.SphereGeometry(1, 32, 16);

// Box with subdivision (for smooth deformation)
const subdividedBox = new THREE.BoxGeometry(1, 1, 1, 4, 4, 4);

// Plane with many segments (for terrain/waves)
const detailedPlane = new THREE.PlaneGeometry(10, 10, 32, 32);`
      }
    ]
  },

  4: {
    id: 4,
    title: "Lighting & Shadows Principles",
    difficulty: "Beginner",
    category: "Core Concepts",
    description: "Learn how to light your 3D scenes like a movie director! Discover different types of lights and how they create mood, depth, and realism.",
    objectives: [
      "Understand different light types and their effects",
      "Learn how to position lights for best results",
      "Create realistic shadows in your scenes",
      "Balance lighting quality with performance"
    ],
    theory: [
      "Lighting in 3D works just like real life - different light sources create different moods and effects in your scenes.",
      "Ambient light provides overall illumination, directional light simulates sunlight, and point lights act like light bulbs.",
      "Shadows add realism and depth to scenes by showing how objects block light from reaching other surfaces.",
      "Good lighting can make simple objects look amazing, while poor lighting can make complex scenes look flat and boring."
    ],
    concepts: [
      { name: "Ambient Light", description: "Overall background lighting that illuminates everything equally", example: "Like the light on a cloudy day - soft and even" },
      { name: "Directional Light", description: "Strong light from one direction, like sunlight", example: "Creates strong shadows and highlights" },
      { name: "Point Light", description: "Light radiating from a single point, like a light bulb", example: "Gets dimmer as you move away from the source" },
      { name: "Shadows", description: "Dark areas where light is blocked by objects", example: "Adds realism and helps show object relationships" }
    ],
    learningPath: [
      {
        id: "step1-ambient-light",
        title: "Step 1: Ambient Light - The Foundation",
        type: "concept",
        content: "Ambient light provides the base level of illumination in your 3D scene, ensuring that no areas are completely black. It simulates the indirect light that bounces around in real environments - light that has reflected off walls, ceilings, and other surfaces to create soft, even illumination from all directions.\n\nIn the real world, even in shadow, objects are rarely completely black because light bounces around the environment. A white ceiling reflects sunlight downward, colored walls tint the ambient light, and even the ground reflects some illumination upward. Ambient light in Three.js simulates this complex light bouncing with a simple, uniform illumination.\n\nAmbient light has no direction and creates no shadows. It affects all surfaces equally regardless of their orientation or position. This makes it perfect for providing base visibility without the complexity of directional lighting calculations. It's computationally efficient because it requires no shadow mapping or complex light calculations.\n\nThe intensity parameter controls how bright the ambient light is. Low values (0.1-0.3) create moody, dramatic scenes with deep shadows. Medium values (0.4-0.6) provide balanced illumination suitable for most applications. High values (0.7-1.0) create bright, evenly lit scenes with minimal contrast.\n\nColor temperature affects the mood of your scene. Cool colors (blues, purples) create nighttime or underwater atmospheres. Warm colors (oranges, yellows) suggest sunset, candlelight, or cozy indoor environments. Neutral colors (grays, whites) provide realistic, balanced illumination.\n\nAmbient light alone creates flat, unrealistic lighting because it lacks directional information. It's almost always combined with directional lights to create depth and dimension. The ratio between ambient and directional lighting determines the overall contrast and mood of your scene.",
        explanation: "Think of ambient light as the background lighting in a photography studio. It fills in the shadows created by the main lights, ensuring that details remain visible in darker areas. Without it, your 3D objects would have harsh, unrealistic shadows with completely black areas.",
        codeExample: `// Create objects to see lighting effects
const sphere = new THREE.Mesh(
  new THREE.SphereGeometry(1, 32, 16),
  new THREE.MeshLambertMaterial({ color: 0x4ecdc4 })
);
sphere.position.x = -1;

const cube = new THREE.Mesh(
  new THREE.BoxGeometry(1, 1, 1),
  new THREE.MeshLambertMaterial({ color: 0xff6b6b })
);
cube.position.x = 1;

scene.add(sphere, cube);

// Add ambient light - soft, even illumination
const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
scene.add(ambientLight);

camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

console.log('💡 Ambient light provides soft, even illumination');
console.log('Try changing the intensity: 0.2 (dim) to 1.0 (bright)');`,
        nextStep: "Next: Add directional light for drama"
      },
      {
        id: "step2-directional-light",
        title: "Step 2: Directional Light - The Sun",
        type: "concept",
        content: "**Directional light** simulates light sources infinitely far away, like the sun. ☀️ All light rays are parallel, creating consistent shadows and strong visual definition.\n\n🌟 **KEY CHARACTERISTICS:**\n📏 **Distance doesn't matter** - object 1 unit away = same intensity as 100 units away\n☀️ **Like sunlight** - sun so far away that rays are essentially parallel\n🎯 **Predictable** - consistent behavior makes it easy to work with\n💪 **Primary light source** in most 3D applications\n💻 `new THREE.DirectionalLight(0xffffff, 1)` // white light, full intensity\n\n🌑 **SHADOW BEHAVIOR:**\n✂️ **Sharp edges** - creates clear, defined shadows\n↔️ **Shadow direction** - opposite to light direction\n📐 **Shadow length** - depends on light angle\n🌅 **Low angle** (sunrise/sunset) = long, dramatic shadows\n🌞 **High angle** (noon) = short shadows beneath objects\n💻 `light.castShadow = true` // enables shadow casting\n\n📍 **POSITION VS DIRECTION:**\n🎯 **Position determines direction only** - light comes from that direction toward origin (0,0,0)\n🔄 **Moving from (5,5,5) to (10,10,10)** = no lighting change (same direction)\n📐 **Only relative direction matters** - not absolute position\n💻 `light.position.set(5, 5, 5)` // light comes from upper-right\n\n💡 **INTENSITY CONTROL:**\n🔆 **Values above 1.0** = overexposed, blown-out highlights\n🔅 **Values below 1.0** = subtle, realistic lighting\n✨ **Material interaction** - shiny materials reflect more, appear brighter\n⚖️ **Sweet spot** - usually 0.5 to 1.5 for realistic scenes\n💻 `light.intensity = 0.8` // slightly dimmed for realism\n\n🎨 **COLOR TEMPERATURE & MOOD:**\n🌙 **Cool colors (blues)** - moonlight, overcast skies, winter scenes\n🔥 **Warm colors (oranges, yellows)** - sunlight, fire, cozy indoor lighting\n🎭 **Color affects materials** - red light makes red objects brighter, green objects darker\n🌈 **Creative possibilities** - purple light for alien worlds, green for underwater\n💻 `new THREE.DirectionalLight(0xffa500, 1)` // warm orange sunlight",
        explanation: "Think of directional light as a giant spotlight infinitely far away, like the sun. No matter where you are on Earth, sunlight comes from the same direction and has the same intensity. This creates the strong shadows and clear definition that help us understand the shape and position of objects in the real world.",
        codeExample: `// Create objects to see directional lighting
const sphere = new THREE.Mesh(
  new THREE.SphereGeometry(1, 32, 16),
  new THREE.MeshLambertMaterial({ color: 0x4ecdc4 })
);
sphere.position.x = -1;

const cube = new THREE.Mesh(
  new THREE.BoxGeometry(1, 1, 1),
  new THREE.MeshLambertMaterial({ color: 0xff6b6b })
);
cube.position.x = 1;

scene.add(sphere, cube);

// Ambient light for base illumination
const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
scene.add(ambientLight);

// Directional light - like sunlight
const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
directionalLight.position.set(5, 5, 5);
scene.add(directionalLight);

camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

console.log('☀️ Directional light creates highlights and shadows');
console.log('Notice how one side is bright and the other is darker');`,
        nextStep: "Next: Add point lights for local illumination"
      },
      {
        id: "step3-point-light",
        title: "Step 3: Point Light - The Light Bulb",
        type: "concept",
        content: "**Point lights** emit light in all directions from a single point, like a light bulb, candle, or star. 💡 Unlike directional lights, they have specific positions and intensity decreases with distance.\n\n🌟 **KEY CHARACTERISTICS:**\n📍 **Specific position** in 3D space\n🔄 **Emits in all directions** (omnidirectional)\n📉 **Intensity decreases** with distance (realistic behavior)\n🎯 **Creates localized lighting** effects\n💻 `new THREE.PointLight(0xffffff, 1, 100)` // color, intensity, distance\n\n📐 **INVERSE SQUARE LAW:**\n🔬 **Real physics** - light intensity decreases proportionally to square of distance\n📏 **Example** - object twice as far = receives 1/4 the light\n✨ **Three.js simulates** this natural behavior automatically\n🧠 **Matches human expectations** for realistic lighting\n💻 `light.decay = 2` // realistic physics decay\n\n🎯 **PERFECT FOR LOCALIZED EFFECTS:**\n🏮 **Street lamps** - illuminate nearby areas, leave distant areas dark\n🔥 **Campfires** - warm, flickering circles of light\n✨ **Magical orbs** - supernatural radiance\n🏠 **Room lighting** - table lamps, overhead lights\n🌟 **Each light** creates its own sphere of influence\n💻 `light.position.set(x, y, z)` // place light in scene\n\n📏 **DISTANCE PARAMETER:**\n🎯 **Controls range** - how far light reaches before fading to zero\n⚡ **Performance crucial** - infinite range affects every object (expensive!)\n🚀 **Limited range** allows many point lights without performance penalty\n🎚️ **Typical values** - 10 to 100 units depending on scene scale\n💻 `new THREE.PointLight(color, intensity, 50)` // light reaches 50 units\n\n📉 **DECAY CONTROL:**\n🔬 **Decay = 1** follows realistic physics\n🎭 **Higher decay** = more dramatic falloff (artistic effects)\n🌅 **Lower decay** = more even illumination over larger areas\n⚖️ **Balance** realism vs artistic vision\n💻 `light.decay = 1.5` // slightly more dramatic than reality\n\n🌑 **SHADOWS (EXPENSIVE!):**\n📦 **Cube map shadows** - renders scene from 6 directions\n💰 **Computationally expensive** - use sparingly\n🎯 **Only when visual impact** justifies performance cost\n⚠️ **Performance tip** - limit to 1-2 shadow-casting point lights\n💻 `light.castShadow = true` // enable shadows (use carefully)\n\n🎨 **MULTIPLE LIGHTS FOR ATMOSPHERE:**\n🏠 **Room example** - overhead lights + table lamps + accent lighting\n🎭 **Layered schemes** create rich, complex atmospheres\n⚖️ **Key challenge** - balance visual richness with performance\n🎯 **Strategy** - few shadow-casting lights + many simple lights\n💻 `scene.add(light1, light2, light3)` // combine multiple lights",
        explanation: "Think of point lights as miniature suns scattered throughout your scene. Each one creates its own little solar system of illumination, with objects closer to the light receiving more energy and objects farther away receiving less, just like planets at different distances from a star.",
        codeExample: `// Create a scene to demonstrate point light
const sphere1 = new THREE.Mesh(
  new THREE.SphereGeometry(0.8, 32, 16),
  new THREE.MeshLambertMaterial({ color: 0x4ecdc4 })
);
sphere1.position.set(-2, 0, 0);

const sphere2 = new THREE.Mesh(
  new THREE.SphereGeometry(0.8, 32, 16),
  new THREE.MeshLambertMaterial({ color: 0xff6b6b })
);
sphere2.position.set(2, 0, 0);

const sphere3 = new THREE.Mesh(
  new THREE.SphereGeometry(0.8, 32, 16),
  new THREE.MeshLambertMaterial({ color: 0xf39c12 })
);
sphere3.position.set(0, 0, -2);

scene.add(sphere1, sphere2, sphere3);

// Point light in the center
const pointLight = new THREE.PointLight(0xffffff, 1, 10);
pointLight.position.set(0, 2, 0);
scene.add(pointLight);

// Small sphere to show where the light is
const lightHelper = new THREE.Mesh(
  new THREE.SphereGeometry(0.1, 8, 8),
  new THREE.MeshBasicMaterial({ color: 0xffff00 })
);
lightHelper.position.copy(pointLight.position);
scene.add(lightHelper);

camera.position.set(0, 3, 5);
camera.lookAt(0, 0, 0);

console.log('💡 Point light radiates from a single point');
console.log('Objects closer to the light are brighter');`,
        nextStep: "Next: Create dramatic shadows"
      },
      {
        id: "step4-shadows",
        title: "Step 4: Shadows - Adding Realism",
        type: "concept",
        content: "**Shadows** are crucial for believable 3D scenes! 🌑 They provide visual cues about object relationships, depth, and lighting direction.\n\n🎯 **WHY SHADOWS MATTER:**\n👻 **Without shadows** - objects appear to float in space\n🔗 **With shadows** - objects are grounded and connected to environment\n👁️ **Visual cues** - help viewers understand spatial relationships\n🌍 **Realism** - proves your 3D world follows real-world rules\n💻 `renderer.shadowMap.enabled = true` // enable shadow system\n\n🗺️ **SHADOW MAPPING TECHNIQUE:**\n📷 **Step 1** - renderer renders scene from light's perspective\n📊 **Step 2** - creates depth map recording distance from light\n🔍 **Step 3** - compares each pixel's distance with stored depth map\n🌑 **Result** - pixels farther than recorded depth = in shadow\n⚡ **Efficient** - modern GPUs handle this process quickly\n💻 `light.shadow.mapSize.width = 2048` // shadow map resolution\n\n🔧 **THREE COMPONENTS REQUIRED:**\n💡 **Light must cast** - `light.castShadow = true`\n📦 **Objects must cast** - `object.castShadow = true`\n🏠 **Surfaces must receive** - `ground.receiveShadow = true`\n🎯 **Granular control** - optimize performance by choosing what participates\n💻 `cube.castShadow = true; plane.receiveShadow = true`\n\n📐 **SHADOW QUALITY:**\n🔍 **Higher resolution** = sharper, more detailed shadows\n💾 **More memory** and processing power required\n📏 **Default resolution** works for most scenes\n🔬 **Close-up views** might need higher resolution for crisp shadows\n💻 `light.shadow.mapSize.setScalar(4096)` // high quality shadows\n\n🐛 **SHADOW BIAS (Fixing Shadow Acne):**\n🔍 **Shadow acne** - surfaces incorrectly shadow themselves (speckled appearance)\n⚖️ **Bias value** - slightly offsets shadow calculation\n⚠️ **Too little bias** = shadow acne\n⚠️ **Too much bias** = shadows detach from objects\n💻 `light.shadow.bias = -0.0001` // fine-tune shadow accuracy\n\n🎨 **DIFFERENT LIGHT TYPES = DIFFERENT SHADOWS:**\n☀️ **Directional lights** - parallel shadows, consistent direction\n💡 **Point lights** - radial shadows spreading outward\n🔦 **Spot lights** - cone-shaped shadow areas with sharp edges\n🎭 **Each type** creates unique shadow characteristics\n💻 `new THREE.SpotLight()` // creates cone-shaped shadows\n\n🚀 **PERFORMANCE OPTIMIZATION:**\n🔢 **Limit shadow-casting lights** (expensive operation)\n📏 **Appropriate shadow map sizes** (balance quality vs performance)\n🎯 **Choose wisely** which objects cast/receive shadows\n💡 **Not every object** needs to participate in shadow calculations\n⚡ **Strategy** - shadows where they matter most visually\n💻 `// Only important objects: hero.castShadow = true`",
        explanation: "Think of shadows as the fingerprints of light. Just as fingerprints reveal unique patterns that help identify individuals, shadows reveal the unique shape, position, and lighting conditions of objects in your scene. They're the visual evidence that proves your 3D world follows the same rules as the real world.",
        codeExample: `// Create objects that will cast and receive shadows
const sphere = new THREE.Mesh(
  new THREE.SphereGeometry(1, 32, 16),
  new THREE.MeshLambertMaterial({ color: 0x4ecdc4 })
);
sphere.position.set(0, 1, 0);
sphere.castShadow = true; // This object will cast shadows

const plane = new THREE.Mesh(
  new THREE.PlaneGeometry(10, 10),
  new THREE.MeshLambertMaterial({ color: 0xcccccc })
);
plane.rotation.x = -Math.PI / 2; // Rotate to be horizontal
plane.position.y = -1;
plane.receiveShadow = true; // This object will receive shadows

scene.add(sphere, plane);

// Directional light that casts shadows
const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
directionalLight.position.set(5, 5, 5);
directionalLight.castShadow = true; // Enable shadow casting
scene.add(directionalLight);

// Ambient light for overall illumination
const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
scene.add(ambientLight);

camera.position.set(3, 3, 3);
camera.lookAt(0, 0, 0);

// Animate the sphere to see shadow movement
animate(() => {
  sphere.position.x = Math.sin(Date.now() * 0.002) * 2;
});

console.log('🌑 Shadows show where light is blocked');
console.log('Watch how the shadow moves with the sphere!');`,
        nextStep: "🎉 You can now light your 3D scenes like a pro!"
      }
    ],
    initialCode: `// 💡 Lighting Your 3D World

// Create objects to light up
const sphere = new THREE.Mesh(
  new THREE.SphereGeometry(1, 32, 16),
  new THREE.MeshLambertMaterial({ color: 0x4ecdc4 })
);
sphere.position.x = -1;

const cube = new THREE.Mesh(
  new THREE.BoxGeometry(1, 1, 1),
  new THREE.MeshLambertMaterial({ color: 0xff6b6b })
);
cube.position.x = 1;

scene.add(sphere, cube);

// Ambient light - soft background lighting
const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
scene.add(ambientLight);

// Directional light - like sunlight
const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
directionalLight.position.set(5, 5, 5);
scene.add(directionalLight);

// Position camera
camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

// Animate to see lighting effects
animate(() => {
  sphere.rotation.y += 0.01;
  cube.rotation.x += 0.01;
});

console.log('💡 Good lighting makes everything look better!');
console.log('🎬 Try changing light positions and intensities');

// Experiment with:
// - ambientLight.intensity (0.1 to 1.0)
// - directionalLight.position.set(x, y, z)
// - directionalLight.intensity (0.1 to 2.0)`,
    tasks: [
      {
        title: "🎬 Movie Director Challenge",
        description: "Create three different lighting moods: bright day, cozy evening, and dramatic night",
        hint: "Adjust ambient and directional light intensities and colors to create different atmospheres"
      },
      {
        title: "💡 Light Positioning",
        description: "Move a point light around your scene and see how it affects the lighting on your objects",
        hint: "Try positioning the light above, below, and to the sides of your objects"
      },
      {
        title: "🌑 Shadow Play",
        description: "Create a scene with multiple objects casting shadows on each other",
        hint: "Remember to set castShadow: true on objects and lights, receiveShadow: true on surfaces"
      }
    ],
    examples: [
      {
        title: "Lighting Comparison",
        description: "See how different lights affect the same objects",
        code: `// Compare different lighting setups
// Setup 1: Only ambient (flat lighting)
const ambientOnly = new THREE.AmbientLight(0x404040, 1);

// Setup 2: Ambient + Directional (dramatic)
const ambient = new THREE.AmbientLight(0x404040, 0.3);
const directional = new THREE.DirectionalLight(0xffffff, 0.8);

// Setup 3: Multiple point lights (party lighting)
const pointLight1 = new THREE.PointLight(0xff0000, 1, 10);
const pointLight2 = new THREE.PointLight(0x00ff00, 1, 10);
const pointLight3 = new THREE.PointLight(0x0000ff, 1, 10);`
      }
    ]
  },

  5: {
    id: 5,
    title: "Texture & Environment Mapping",
    difficulty: "Intermediate",
    category: "Materials",
    description: "Learn how to add realistic textures and reflections to your 3D objects! Discover how to wrap 2D images around 3D shapes and create shiny, reflective surfaces.",
    objectives: [
      "Understand how 2D textures wrap around 3D objects",
      "Learn to create materials with different surface properties",
      "Explore reflective and metallic materials",
      "Combine textures for realistic effects"
    ],
    theory: [
      "Textures are like wallpaper for your 3D objects - they add color, detail, and realism to otherwise plain surfaces.",
      "UV mapping is how we tell the computer how to wrap a flat 2D image around a curved 3D object, like wrapping paper around a gift.",
      "Different texture types serve different purposes: diffuse maps add color, normal maps add surface detail, and environment maps create reflections.",
      "Material properties like metalness and roughness control how light bounces off surfaces, making objects look like metal, plastic, or other materials."
    ],
    concepts: [
      { name: "Texture Mapping", description: "Wrapping 2D images around 3D objects", example: "Like putting a sticker on a ball - the flat sticker conforms to the curved surface" },
      { name: "Material Properties", description: "Metalness, roughness, and other surface characteristics", example: "Shiny metal vs rough plastic vs smooth glass" },
      { name: "Environment Mapping", description: "Reflections of the surrounding environment", example: "Chrome sphere reflecting the world around it" },
      { name: "UV Coordinates", description: "How 2D texture coordinates map to 3D surfaces", example: "Like a map projection - flattening a globe onto paper" }
    ],
    learningPath: [
      {
        id: "step1-basic-textures",
        title: "Step 1: Adding Color with Textures",
        type: "concept",
        content: "**Textures** transform flat, uniform 3D objects into rich, detailed surfaces! 🎨 Instead of solid colors, you can wrap 2D images around 3D geometry.\n\n✨ **WHAT TEXTURES CAN BE:**\n📸 **Photographs** - realistic surface details\n🎨 **Hand-painted artwork** - stylized, artistic looks\n🔄 **Procedural patterns** - mathematically generated designs\n🌟 **Any visual content** you can imagine\n🌍 **Example transformations** - sphere → planet, basketball, magical orb\n💻 `new THREE.TextureLoader().load('image.jpg')`\n\n🗺️ **UV COORDINATE MAPPING:**\n📦 **Like gift wrapping** - flat paper (texture) around 3D object (geometry)\n🗺️ **UV coordinates** map every 3D surface point to 2D texture point\n📐 **U = horizontal** (0 to 1), **V = vertical** (0 to 1)\n🎯 **Computer calculates** exactly how to stretch and position texture\n💻 `geometry.attributes.uv` // contains UV mapping data\n\n📏 **TEXTURE RESOLUTION:**\n🔍 **High-res (2048x2048+)** - crisp detail, more memory/bandwidth\n⚡ **Low-res (256x256-)** - loads quickly, may appear blurry\n🎯 **Key strategy** - match resolution to object importance and viewing distance\n📱 **Mobile consideration** - lower resolutions for better performance\n💻 `texture.image.width` // check texture dimensions\n\n📁 **TEXTURE FORMATS:**\n📸 **JPEG** - small files, photographic textures, NO transparency\n🖼️ **PNG** - supports transparency, larger file sizes\n🚀 **WebP** - best compression for web delivery\n⚡ **Compressed formats** - optimized for GPU memory (real-time apps)\n💻 `loader.load('texture.webp')` // modern web format\n\n🔍 **TEXTURE FILTERING:**\n📐 **Linear filtering** - smooths textures when magnified (prevents pixelation)\n🔄 **Mipmapping** - multiple resolution versions, auto-selects based on distance\n⚡ **Performance benefit** - prevents aliasing, improves rendering speed\n👁️ **Visual quality** - textures look good at any distance\n💻 `texture.generateMipmaps = true` // enable mipmapping\n\n🔄 **TEXTURE WRAPPING:**\n🔁 **Repeat wrapping** - tiles texture across surface (brick, fabric patterns)\n📏 **Clamp wrapping** - stretches edge pixels (unique textures, no repeat)\n🪞 **Mirror wrapping** - flips alternate tiles (seamless patterns)\n🎨 **Creative control** - choose wrapping for desired visual effect\n💻 `texture.wrapS = THREE.RepeatWrapping` // horizontal repeat\n💻 `texture.wrapT = THREE.RepeatWrapping` // vertical repeat",
        explanation: "Think of textures as the skin, clothing, or paint job for your 3D objects. Just as the same person can look completely different with different clothing and makeup, the same 3D geometry can represent entirely different objects depending on the texture applied to it.",
        codeExample: `// Create objects with different surface appearances
const geometry = new THREE.SphereGeometry(1, 32, 16);

// Solid color material (no texture)
const solidSphere = new THREE.Mesh(
  geometry,
  new THREE.MeshLambertMaterial({ color: 0xff6b6b })
);
solidSphere.position.x = -2;

// Textured material using a simple pattern
const canvas = document.createElement('canvas');
canvas.width = 64;
canvas.height = 64;
const context = canvas.getContext('2d');

// Create a checkerboard pattern
for (let i = 0; i < 8; i++) {
  for (let j = 0; j < 8; j++) {
    context.fillStyle = (i + j) % 2 ? '#ffffff' : '#000000';
    context.fillRect(i * 8, j * 8, 8, 8);
  }
}

const texture = new THREE.CanvasTexture(canvas);
const texturedSphere = new THREE.Mesh(
  geometry,
  new THREE.MeshLambertMaterial({ map: texture })
);
texturedSphere.position.x = 2;

scene.add(solidSphere, texturedSphere);
camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

console.log('🎨 Textures add visual detail to 3D objects');
console.log('Left: solid color, Right: checkerboard texture');`,
        nextStep: "Next: Create metallic and shiny materials"
      },
      {
        id: "step2-metallic-materials",
        title: "Step 2: Metallic and Shiny Materials",
        type: "concept",
        content: "Some materials like metal, glass, and plastic reflect light differently than others. We can control how shiny, metallic, or rough a surface looks to create realistic materials.",
        explanation: "Real materials have different properties - metal is shiny and reflective, wood is rough and matte, plastic can be anywhere in between. Three.js lets us simulate these properties to make objects look like real materials.",
        codeExample: `// Create materials with different surface properties
const geometry = new THREE.SphereGeometry(0.8, 32, 16);

// Matte material (like chalk or paper)
const matteSphere = new THREE.Mesh(
  geometry,
  new THREE.MeshStandardMaterial({
    color: 0xff6b6b,
    metalness: 0,    // Not metallic
    roughness: 1     // Very rough (matte)
  })
);
matteSphere.position.x = -2;

// Plastic material (slightly shiny)
const plasticSphere = new THREE.Mesh(
  geometry,
  new THREE.MeshStandardMaterial({
    color: 0x4ecdc4,
    metalness: 0,    // Not metallic
    roughness: 0.3   // Somewhat smooth
  })
);
plasticSphere.position.x = 0;

// Metal material (very shiny and reflective)
const metalSphere = new THREE.Mesh(
  geometry,
  new THREE.MeshStandardMaterial({
    color: 0xf39c12,
    metalness: 1,    // Fully metallic
    roughness: 0.1   // Very smooth
  })
);
metalSphere.position.x = 2;

scene.add(matteSphere, plasticSphere, metalSphere);

// Add lights to see the material differences
const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
directionalLight.position.set(5, 5, 5);
scene.add(ambientLight, directionalLight);

camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

console.log('✨ Different materials reflect light differently');
console.log('Left: matte, Middle: plastic, Right: metal');`,
        nextStep: "Next: Create environment reflections"
      },
      {
        id: "step3-environment-reflections",
        title: "Step 3: Environment Reflections",
        type: "concept",
        content: "Shiny objects reflect their surroundings, just like a mirror or chrome surface. We can create this effect by using environment maps that show reflections of the world around the object.",
        explanation: "When you look at a chrome car bumper, you see the reflection of the street, sky, and buildings around it. Environment mapping lets us create this same effect in 3D by reflecting a 360-degree image of the surroundings.",
        codeExample: `// Create a simple environment for reflections
const geometry = new THREE.SphereGeometry(1, 32, 16);

// Create a simple cube texture for environment mapping
const cubeRenderTarget = new THREE.WebGLCubeRenderTarget(256);
const cubeCamera = new THREE.CubeCamera(0.1, 1000, cubeRenderTarget);

// Create some colorful objects to reflect
const redCube = new THREE.Mesh(
  new THREE.BoxGeometry(1, 1, 1),
  new THREE.MeshBasicMaterial({ color: 0xff0000 })
);
redCube.position.set(-3, 0, 0);

const greenCube = new THREE.Mesh(
  new THREE.BoxGeometry(1, 1, 1),
  new THREE.MeshBasicMaterial({ color: 0x00ff00 })
);
greenCube.position.set(3, 0, 0);

const blueCube = new THREE.Mesh(
  new THREE.BoxGeometry(1, 1, 1),
  new THREE.MeshBasicMaterial({ color: 0x0000ff })
);
blueCube.position.set(0, 0, -3);

scene.add(redCube, greenCube, blueCube);

// Reflective sphere in the center
const reflectiveSphere = new THREE.Mesh(
  geometry,
  new THREE.MeshStandardMaterial({
    color: 0xffffff,
    metalness: 1,
    roughness: 0,
    envMapIntensity: 1
  })
);
scene.add(reflectiveSphere);

// Set up environment mapping
scene.add(cubeCamera);
reflectiveSphere.material.envMap = cubeRenderTarget.texture;

camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

console.log('🪞 Reflective materials show their environment');
console.log('The sphere reflects the colored cubes around it');`,
        nextStep: "Next: Combine textures for complex materials"
      },
      {
        id: "step4-complex-materials",
        title: "Step 4: Combining Textures",
        type: "concept",
        content: "Real materials often combine multiple textures - a base color, surface roughness, and metallic properties. By layering these textures, we can create incredibly realistic materials.",
        explanation: "Think of a rusty metal surface - it has areas that are still shiny metal and areas that are rough rust. By combining different texture maps, we can create these complex, realistic materials that tell a story.",
        codeExample: `// Create a complex material with multiple properties
const geometry = new THREE.SphereGeometry(1, 32, 16);

// Create a base color texture (simple pattern)
const colorCanvas = document.createElement('canvas');
colorCanvas.width = 128;
colorCanvas.height = 128;
const colorContext = colorCanvas.getContext('2d');

// Create a gradient from blue to red
const gradient = colorContext.createLinearGradient(0, 0, 128, 128);
gradient.addColorStop(0, '#4ecdc4');
gradient.addColorStop(1, '#ff6b6b');
colorContext.fillStyle = gradient;
colorContext.fillRect(0, 0, 128, 128);

const colorTexture = new THREE.CanvasTexture(colorCanvas);

// Create a roughness texture (controls shininess)
const roughnessCanvas = document.createElement('canvas');
roughnessCanvas.width = 128;
roughnessCanvas.height = 128;
const roughnessContext = roughnessCanvas.getContext('2d');

// Create random spots of different roughness
for (let i = 0; i < 50; i++) {
  const x = Math.random() * 128;
  const y = Math.random() * 128;
  const radius = Math.random() * 20 + 5;
  const brightness = Math.random() * 255;

  roughnessContext.fillStyle = \`rgb(\${brightness}, \${brightness}, \${brightness})\`;
  roughnessContext.beginPath();
  roughnessContext.arc(x, y, radius, 0, Math.PI * 2);
  roughnessContext.fill();
}

const roughnessTexture = new THREE.CanvasTexture(roughnessCanvas);

// Complex material combining multiple textures
const complexSphere = new THREE.Mesh(
  geometry,
  new THREE.MeshStandardMaterial({
    map: colorTexture,           // Base color
    roughnessMap: roughnessTexture, // Surface roughness variation
    metalness: 0.7,              // Mostly metallic
    roughness: 0.3               // Base roughness level
  })
);

scene.add(complexSphere);

// Add lighting to see the material complexity
const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
directionalLight.position.set(5, 5, 5);
scene.add(ambientLight, directionalLight);

camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

// Animate to see how light plays across the surface
animate(() => {
  complexSphere.rotation.y += 0.01;
});

console.log('🎭 Complex materials combine multiple texture layers');
console.log('Watch how different areas reflect light differently');`,
        nextStep: "🎉 You can now create realistic materials and textures!"
      }
    ],
    initialCode: `// 🎨 Textures and Materials - Making Objects Look Real

// Create objects to apply different materials to
const geometry = new THREE.SphereGeometry(1, 32, 16);

// Basic colored material
const colorSphere = new THREE.Mesh(
  geometry,
  new THREE.MeshStandardMaterial({ color: 0xff6b6b })
);
colorSphere.position.x = -2;

// Metallic material
const metalSphere = new THREE.Mesh(
  geometry,
  new THREE.MeshStandardMaterial({
    color: 0x4ecdc4,
    metalness: 1,
    roughness: 0.1
  })
);
metalSphere.position.x = 0;

// Rough matte material
const matteSphere = new THREE.Mesh(
  geometry,
  new THREE.MeshStandardMaterial({
    color: 0xf39c12,
    metalness: 0,
    roughness: 1
  })
);
matteSphere.position.x = 2;

scene.add(colorSphere, metalSphere, matteSphere);

// Add lighting to see material differences
const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
directionalLight.position.set(5, 5, 5);
scene.add(ambientLight, directionalLight);

camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

// Animate to see how light plays on different materials
animate(() => {
  colorSphere.rotation.y += 0.01;
  metalSphere.rotation.y += 0.01;
  matteSphere.rotation.y += 0.01;
});

console.log('🎨 Different materials react to light differently');
console.log('🔧 Try adjusting metalness (0-1) and roughness (0-1)');`,
    tasks: [
      {
        title: "🏀 Sports Ball Collection",
        description: "Create different sports balls using textures and materials - basketball, soccer ball, tennis ball",
        hint: "Use different colors, patterns, and material properties to make each ball look unique"
      },
      {
        title: "🪞 Mirror Gallery",
        description: "Create a gallery of objects with different levels of reflectivity - from matte to mirror-like",
        hint: "Experiment with metalness and roughness values to create different reflection levels"
      },
      {
        title: "🎭 Material Storytelling",
        description: "Create objects that tell a story through their materials - rusty metal, polished wood, worn plastic",
        hint: "Combine different material properties to suggest age, wear, or different material types"
      }
    ],
    examples: [
      {
        title: "Material Property Guide",
        description: "Understanding metalness and roughness values",
        code: `// Material property combinations:
// Matte paint: metalness: 0, roughness: 1
// Plastic: metalness: 0, roughness: 0.3-0.7
// Polished metal: metalness: 1, roughness: 0-0.2
// Brushed metal: metalness: 1, roughness: 0.3-0.6
// Rubber: metalness: 0, roughness: 0.8-1.0
// Glass: metalness: 0, roughness: 0-0.1`
      }
    ]
  },

  6: {
    id: 6,
    title: "Animation & Timing Concepts",
    difficulty: "Intermediate",
    category: "Animation",
    description: "Bring your 3D worlds to life with smooth, natural animations! Learn different animation techniques and how to make objects move in believable, engaging ways.",
    objectives: [
      "Create smooth animations using different timing methods",
      "Understand easing functions for natural motion",
      "Combine multiple animations for complex sequences",
      "Control animation timing and synchronization"
    ],
    theory: [
      "Animation is the art of bringing static objects to life through motion. In 3D, we can animate position, rotation, scale, color, and many other properties.",
      "Different easing functions create different feelings - linear motion feels robotic, while eased motion feels natural and organic.",
      "Timing is crucial in animation - fast movements feel energetic, slow movements feel calm, and the rhythm creates the mood.",
      "Complex animations often combine multiple simple movements happening at different times to create sophisticated, choreographed sequences."
    ],
    concepts: [
      { name: "Linear vs Eased Motion", description: "Constant speed vs natural acceleration/deceleration", example: "Robot movement vs human movement" },
      { name: "Animation Properties", description: "Position, rotation, scale, color, and material properties", example: "Moving, spinning, growing, changing color" },
      { name: "Timing Functions", description: "How motion changes over time", example: "Ease-in (slow start), ease-out (slow end), bounce, elastic" },
      { name: "Animation Sequences", description: "Multiple animations working together", example: "Object moves, then rotates, then changes color" }
    ],
    learningPath: [
      {
        id: "step1-basic-animation",
        title: "Step 1: Your First Animation",
        type: "concept",
        content: "**Animation** in 3D is about changing object properties over time! ⚡ The simplest animations bring static scenes to life with movement that catches the eye.\n\n🎬 **FUNDAMENTAL CONCEPT:**\n📝 **Change properties** over time (position, rotation, scale)\n👁️ **Creates movement** that catches attention\n✨ **Brings scenes to life** - static → dynamic\n🎯 **Core principle** - small changes each frame = smooth motion\n💻 `cube.rotation.y += 0.01` // rotate slightly each frame\n\n🔄 **BASIC ANIMATION TYPES:**\n📍 **Position animation** - objects move through space\n🌀 **Rotation animation** - objects spin or turn\n📏 **Scale animation** - objects grow, shrink, or pulse\n🎨 **Color animation** - materials change hue over time\n👻 **Opacity animation** - objects fade in/out\n💻 `object.position.x += speed` // move horizontally\n\n⏰ **ANIMATION LOOP INTEGRATION:**\n🔁 **RequestAnimationFrame** - browser's timing system\n📊 **60 FPS target** - smooth, professional motion\n🎯 **Update properties** in animation loop\n🎨 **Render scene** after updates\n💻 `function animate() { /* update properties */ renderer.render(scene, camera); }`\n\n📐 **MATHEMATICAL FOUNDATIONS:**\n➕ **Linear motion** - constant speed changes\n📈 **Sine/cosine waves** - smooth, natural oscillations\n🎯 **Delta time** - frame-rate independent animation\n⚡ **Performance** - efficient calculations for smooth motion\n💻 `Math.sin(time * speed)` // creates wave motion\n\n🎨 **VISUAL IMPACT:**\n👁️ **Draws attention** - moving objects catch the eye\n🎭 **Creates personality** - how objects move defines character\n🌟 **Enhances storytelling** - motion conveys emotion and meaning\n🎯 **User engagement** - interactive, dynamic experiences\n💻 `// Gentle bobbing: object.position.y = Math.sin(time) * 0.5`",
        explanation: "Think of animation like a flipbook - each frame shows the object in a slightly different position. When played quickly, these small changes create the illusion of smooth motion.",
        codeExample: `// Create objects to animate
const cube = new THREE.Mesh(
  new THREE.BoxGeometry(1, 1, 1),
  new THREE.MeshBasicMaterial({ color: 0xff6b6b })
);
cube.position.x = -2;

const sphere = new THREE.Mesh(
  new THREE.SphereGeometry(0.7, 32, 16),
  new THREE.MeshBasicMaterial({ color: 0x4ecdc4 })
);
sphere.position.x = 0;

const cylinder = new THREE.Mesh(
  new THREE.CylinderGeometry(0.5, 0.5, 1, 8),
  new THREE.MeshBasicMaterial({ color: 0xf39c12 })
);
cylinder.position.x = 2;

scene.add(cube, sphere, cylinder);
camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

// Simple animations - each object moves differently
animate(() => {
  // Rotate the cube
  cube.rotation.y += 0.02;

  // Bounce the sphere up and down
  sphere.position.y = Math.sin(Date.now() * 0.003) * 1.5;

  // Scale the cylinder bigger and smaller
  const scale = 1 + Math.sin(Date.now() * 0.004) * 0.5;
  cylinder.scale.set(scale, 1, scale);
});

console.log('🎬 Basic animations: rotation, position, and scale');
console.log('Each object has its own unique movement pattern');`,
        nextStep: "Next: Learn about easing for natural motion"
      },
      {
        id: "step2-easing-functions",
        title: "Step 2: Natural Motion with Easing",
        type: "concept",
        content: "Real objects don't move at constant speed - they accelerate when starting and decelerate when stopping. Easing functions help us create this natural-feeling motion in our animations.",
        explanation: "Think about how a ball bounces or how a car starts moving. Nothing in real life moves at perfectly constant speed - there's always acceleration and deceleration that makes motion feel natural and believable.",
        codeExample: `// Create objects to demonstrate different easing
const linearSphere = new THREE.Mesh(
  new THREE.SphereGeometry(0.5, 32, 16),
  new THREE.MeshBasicMaterial({ color: 0xff0000 })
);
linearSphere.position.z = 1;

const easedSphere = new THREE.Mesh(
  new THREE.SphereGeometry(0.5, 32, 16),
  new THREE.MeshBasicMaterial({ color: 0x00ff00 })
);
easedSphere.position.z = -1;

scene.add(linearSphere, easedSphere);
camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

// Animation with different easing
animate(() => {
  const time = Date.now() * 0.001;

  // Linear motion - constant speed (feels robotic)
  linearSphere.position.x = Math.sin(time) * 3;

  // Eased motion - natural acceleration/deceleration
  const easedTime = time * 0.7; // Slower for comparison
  const easeInOut = (t) => {
    // Smooth ease-in-out function
    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
  };

  const normalizedSin = (Math.sin(easedTime) + 1) / 2; // 0 to 1
  const easedValue = easeInOut(normalizedSin);
  easedSphere.position.x = (easedValue - 0.5) * 6; // -3 to 3
});

console.log('🎭 Compare linear (red) vs eased (green) motion');
console.log('Notice how the green sphere feels more natural');`,
        nextStep: "Next: Create bouncing and elastic effects"
      },
      {
        id: "step3-bouncing-effects",
        title: "Step 3: Bouncing and Elastic Effects",
        type: "concept",
        content: "Some of the most engaging animations have bounce, elasticity, or overshoot effects. These make objects feel like they have weight and physics, creating more dynamic and playful animations.",
        explanation: "Think of a rubber ball hitting the ground, or a spring being stretched and released. These natural physics behaviors make animations feel alive and give objects personality and character.",
        codeExample: `// Create objects with different bounce effects
const bouncyBall = new THREE.Mesh(
  new THREE.SphereGeometry(0.6, 32, 16),
  new THREE.MeshBasicMaterial({ color: 0xff6b6b })
);
bouncyBall.position.x = -2;

const elasticCube = new THREE.Mesh(
  new THREE.BoxGeometry(1, 1, 1),
  new THREE.MeshBasicMaterial({ color: 0x4ecdc4 })
);
elasticCube.position.x = 2;

scene.add(bouncyBall, elasticCube);
camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

// Bouncing and elastic animations
animate(() => {
  const time = Date.now() * 0.002;

  // Bouncing ball effect
  const bounceHeight = Math.abs(Math.sin(time * 2)) * 2;
  bouncyBall.position.y = bounceHeight;

  // Add squash and stretch for more realistic bounce
  const squash = 1 - (bounceHeight / 4); // Squash when low
  bouncyBall.scale.set(1 + squash * 0.3, 1 - squash * 0.3, 1 + squash * 0.3);

  // Elastic overshoot effect
  const elasticTime = time * 1.5;
  const overshoot = Math.sin(elasticTime) * Math.exp(-elasticTime * 0.3);
  elasticCube.rotation.y = overshoot * 2;

  // Elastic scale effect
  const elasticScale = 1 + overshoot * 0.5;
  elasticCube.scale.set(elasticScale, elasticScale, elasticScale);
});

console.log('🏀 Bouncing ball with squash and stretch');
console.log('📦 Elastic cube with overshoot effect');`,
        nextStep: "Next: Coordinate multiple animations"
      },
      {
        id: "step4-animation-sequences",
        title: "Step 4: Animation Sequences",
        type: "concept",
        content: "The most impressive animations combine multiple movements in sequence or simultaneously. Learn to choreograph complex animations that tell a story through motion.",
        explanation: "Think of a dance or a magic trick - multiple things happen in a specific order to create a complete performance. Animation sequences work the same way, combining simple movements into complex, engaging experiences.",
        codeExample: `// Create objects for a choreographed sequence
const dancer1 = new THREE.Mesh(
  new THREE.CylinderGeometry(0.3, 0.3, 1.5, 8),
  new THREE.MeshBasicMaterial({ color: 0xff6b6b })
);
dancer1.position.x = -1;

const dancer2 = new THREE.Mesh(
  new THREE.CylinderGeometry(0.3, 0.3, 1.5, 8),
  new THREE.MeshBasicMaterial({ color: 0x4ecdc4 })
);
dancer2.position.x = 1;

const centerPiece = new THREE.Mesh(
  new THREE.SphereGeometry(0.5, 32, 16),
  new THREE.MeshBasicMaterial({ color: 0xf39c12 })
);

scene.add(dancer1, dancer2, centerPiece);
camera.position.set(0, 3, 6);
camera.lookAt(0, 0, 0);

// Choreographed animation sequence
animate(() => {
  const time = Date.now() * 0.001;

  // Dancer 1: Spins and moves in a circle
  const angle1 = time * 2;
  dancer1.position.x = Math.cos(angle1) * 2;
  dancer1.position.z = Math.sin(angle1) * 2;
  dancer1.rotation.y = angle1 * 2;

  // Dancer 2: Counter-clockwise with different timing
  const angle2 = -time * 1.5 + Math.PI; // Offset by PI
  dancer2.position.x = Math.cos(angle2) * 2;
  dancer2.position.z = Math.sin(angle2) * 2;
  dancer2.rotation.y = -angle2 * 1.5;

  // Center piece: Bounces and changes color
  centerPiece.position.y = Math.abs(Math.sin(time * 3)) * 1.5;

  // Color animation using HSL
  const hue = (time * 50) % 360;
  centerPiece.material.color.setHSL(hue / 360, 1, 0.5);

  // Scale pulse
  const pulse = 1 + Math.sin(time * 4) * 0.3;
  centerPiece.scale.set(pulse, pulse, pulse);
});

console.log('💃 Choreographed animation sequence');
console.log('Multiple objects moving in harmony');`,
        nextStep: "🎉 You can now create engaging animations!"
      }
    ],
    initialCode: `// 🎬 Animation Playground - Bringing Objects to Life

// Create objects to animate
const spinningCube = new THREE.Mesh(
  new THREE.BoxGeometry(1, 1, 1),
  new THREE.MeshBasicMaterial({ color: 0xff6b6b })
);
spinningCube.position.x = -2;

const bouncingSphere = new THREE.Mesh(
  new THREE.SphereGeometry(0.7, 32, 16),
  new THREE.MeshBasicMaterial({ color: 0x4ecdc4 })
);
bouncingSphere.position.x = 0;

const pulsingCylinder = new THREE.Mesh(
  new THREE.CylinderGeometry(0.5, 0.5, 1, 8),
  new THREE.MeshBasicMaterial({ color: 0xf39c12 })
);
pulsingCylinder.position.x = 2;

scene.add(spinningCube, bouncingSphere, pulsingCylinder);
camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

// Different animation styles
animate(() => {
  const time = Date.now() * 0.001;

  // Spinning cube - constant rotation
  spinningCube.rotation.x += 0.02;
  spinningCube.rotation.y += 0.01;

  // Bouncing sphere - sine wave motion
  bouncingSphere.position.y = Math.sin(time * 3) * 1.5;

  // Pulsing cylinder - scale animation
  const pulse = 1 + Math.sin(time * 4) * 0.5;
  pulsingCylinder.scale.set(pulse, 1, pulse);

  // Color animation
  const hue = (time * 30) % 360;
  pulsingCylinder.material.color.setHSL(hue / 360, 1, 0.5);
});

console.log('🎭 Three different animation styles');
console.log('🔧 Try changing the timing and math functions!');

// Experiment with:
// - Different rotation speeds: += 0.01, += 0.05
// - Different bounce patterns: Math.sin(time * 2), Math.cos(time)
// - Different scale effects: pulse * 0.3, pulse * 0.8`,
    tasks: [
      {
        title: "🎪 Animation Circus",
        description: "Create a circus scene with multiple performers doing different tricks - spinning, jumping, balancing",
        hint: "Use different math functions like sin, cos, and combine position, rotation, and scale animations"
      },
      {
        title: "🌊 Wave Motion",
        description: "Create a row of objects that move in a wave pattern, like a stadium wave or ocean waves",
        hint: "Use a loop to create multiple objects, then offset their animation timing based on their position"
      },
      {
        title: "🎵 Music Visualizer",
        description: "Create objects that pulse and move as if responding to music beats",
        hint: "Use different frequencies and amplitudes to make objects dance to an imaginary rhythm"
      }
    ],
    examples: [
      {
        title: "Easing Function Library",
        description: "Common easing functions for natural motion",
        code: `// Easing functions for natural animation
const easeInOut = (t) => t < 0.5 ? 2*t*t : -1+(4-2*t)*t;
const easeIn = (t) => t*t;
const easeOut = (t) => t*(2-t);
const bounce = (t) => {
  if (t < 1/2.75) return 7.5625*t*t;
  if (t < 2/2.75) return 7.5625*(t-=1.5/2.75)*t + 0.75;
  if (t < 2.5/2.75) return 7.5625*(t-=2.25/2.75)*t + 0.9375;
  return 7.5625*(t-=2.625/2.75)*t + 0.984375;
};`
      }
    ]
  },

  7: {
    id: 7,
    title: "Interaction & Control Workflows",
    difficulty: "Intermediate",
    category: "Interaction",
    description: "Make your 3D scenes interactive! Learn how to detect mouse clicks on objects, respond to user input, and create engaging interactive experiences.",
    objectives: [
      "Detect when users click on 3D objects",
      "Create visual feedback for user interactions",
      "Understand different types of camera controls",
      "Build responsive interactive interfaces"
    ],
    theory: [
      "Interactive 3D experiences respond to user input - mouse clicks, hovers, and movements. This makes static scenes come alive and engage users.",
      "Raycasting is like shooting an invisible laser from the camera through the mouse cursor to see what 3D object it hits first.",
      "Good interaction design provides immediate feedback - objects should respond visually when users hover or click on them.",
      "Camera controls let users explore 3D scenes by moving the viewpoint, making them feel like they're really inside the 3D world."
    ],
    concepts: [
      { name: "Raycasting", description: "Detecting which 3D object the mouse is pointing at", example: "Like pointing a laser pointer at objects in a room" },
      { name: "Visual Feedback", description: "Objects change appearance when interacted with", example: "Highlighting, color changes, scaling effects" },
      { name: "Camera Controls", description: "Letting users move around the 3D scene", example: "Orbit around objects, zoom in/out, pan the view" },
      { name: "Event Handling", description: "Responding to mouse clicks, hovers, and movements", example: "Click to select, hover to highlight" }
    ],
    learningPath: [
      {
        id: "step1-mouse-detection",
        title: "Step 1: Detecting Mouse Clicks",
        type: "concept",
        content: "The first step in making interactive 3D scenes is detecting when users click on objects. We use raycasting - imagine shooting an invisible ray from the camera through the mouse cursor to see what it hits.",
        explanation: "Raycasting works like pointing a laser pointer at objects in a room. The computer calculates which object the invisible ray hits first, then we can make that object respond to the click.",
        codeExample: `// Create clickable objects
const redCube = new THREE.Mesh(
  new THREE.BoxGeometry(1, 1, 1),
  new THREE.MeshBasicMaterial({ color: 0xff6b6b })
);
redCube.position.x = -2;

const blueSphere = new THREE.Mesh(
  new THREE.SphereGeometry(0.7, 32, 16),
  new THREE.MeshBasicMaterial({ color: 0x4ecdc4 })
);
blueSphere.position.x = 0;

const greenCylinder = new THREE.Mesh(
  new THREE.CylinderGeometry(0.5, 0.5, 1, 8),
  new THREE.MeshBasicMaterial({ color: 0x2ecc71 })
);
greenCylinder.position.x = 2;

scene.add(redCube, blueSphere, greenCylinder);

// Set up raycasting
const raycaster = new THREE.Raycaster();
const mouse = new THREE.Vector2();
const clickableObjects = [redCube, blueSphere, greenCylinder];

// Mouse click detection
const onMouseClick = (event) => {
  // Get canvas bounds for accurate coordinate mapping
  const canvas = renderer.domElement;
  const rect = canvas.getBoundingClientRect();

  // Convert mouse position to normalized coordinates (-1 to 1)
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

  // Cast a ray from camera through mouse position
  raycaster.setFromCamera(mouse, camera);

  // Check which objects the ray intersects
  const intersects = raycaster.intersectObjects(clickableObjects);

  if (intersects.length > 0) {
    const clickedObject = intersects[0].object;
    console.log('Clicked on object!', clickedObject);

    // Make the object spin when clicked
    clickedObject.rotation.y += Math.PI / 4;
  }
};

// Add event listener
window.addEventListener('click', onMouseClick);

camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

console.log('🖱️ Click on the objects to make them spin!');`,
        nextStep: "Next: Add visual feedback for better UX"
      },
      {
        id: "step2-visual-feedback",
        title: "Step 2: Visual Feedback",
        type: "concept",
        content: "Good interactive design gives users immediate feedback. When they hover over or click on objects, something should happen visually to show that the interaction was detected.",
        explanation: "Think of buttons on a website - they change color when you hover over them and press down when clicked. 3D objects should behave similarly to feel responsive and alive.",
        codeExample: `// Create interactive objects with feedback
const interactiveObjects = [];

for (let i = 0; i < 3; i++) {
  const object = new THREE.Mesh(
    new THREE.BoxGeometry(1, 1, 1),
    new THREE.MeshBasicMaterial({ color: 0x4ecdc4 })
  );
  object.position.x = (i - 1) * 2;
  object.userData = {
    originalColor: 0x4ecdc4,
    hoverColor: 0xff6b6b,
    isHovered: false
  };
  scene.add(object);
  interactiveObjects.push(object);
}

// Set up interaction
const raycaster = new THREE.Raycaster();
const mouse = new THREE.Vector2();

// Mouse move for hover effects
const onMouseMove = (event) => {
  // Get canvas bounds for accurate coordinate mapping
  const canvas = renderer.domElement;
  const rect = canvas.getBoundingClientRect();

  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

  raycaster.setFromCamera(mouse, camera);
  const intersects = raycaster.intersectObjects(interactiveObjects);

  // Reset all objects to normal state
  interactiveObjects.forEach(obj => {
    if (obj.userData.isHovered) {
      obj.material.color.setHex(obj.userData.originalColor);
      obj.scale.set(1, 1, 1);
      obj.userData.isHovered = false;
    }
  });

  // Highlight hovered object
  if (intersects.length > 0) {
    const hoveredObject = intersects[0].object;
    hoveredObject.material.color.setHex(hoveredObject.userData.hoverColor);
    hoveredObject.scale.set(1.2, 1.2, 1.2); // Make it bigger
    hoveredObject.userData.isHovered = true;
  }
};

// Mouse click for selection
const onMouseClick = (event) => {
  raycaster.setFromCamera(mouse, camera);
  const intersects = raycaster.intersectObjects(interactiveObjects);

  if (intersects.length > 0) {
    const clickedObject = intersects[0].object;

    // Spin animation on click
    const startRotation = clickedObject.rotation.y;
    const targetRotation = startRotation + Math.PI * 2;

    animate(() => {
      if (clickedObject.rotation.y < targetRotation) {
        clickedObject.rotation.y += 0.1;
      }
    });

    console.log('Object clicked and spinning!');
  }
};

window.addEventListener('mousemove', onMouseMove);
window.addEventListener('click', onMouseClick);

camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

console.log('🎯 Hover to highlight, click to spin!');`,
        nextStep: "Next: Learn about camera controls"
      },
      {
        id: "step3-camera-controls",
        title: "Step 3: Camera Controls",
        type: "concept",
        content: "Camera controls let users explore your 3D scene by moving the viewpoint around. This makes them feel like they're really inside the 3D world, able to look around and examine objects from different angles.",
        explanation: "Think of camera controls like being able to walk around a sculpture in a museum - you can circle around it, get closer for details, or step back to see the whole thing. This freedom of movement makes 3D experiences much more engaging.",
        codeExample: `// Create a scene worth exploring
const objects = [];

// Create a collection of objects to explore
for (let i = 0; i < 5; i++) {
  for (let j = 0; j < 5; j++) {
    const geometry = Math.random() > 0.5
      ? new THREE.BoxGeometry(0.5, 0.5, 0.5)
      : new THREE.SphereGeometry(0.3, 16, 16);

    const material = new THREE.MeshBasicMaterial({
      color: new THREE.Color().setHSL(Math.random(), 0.8, 0.6)
    });

    const object = new THREE.Mesh(geometry, material);
    object.position.set(
      (i - 2) * 1.5,
      Math.random() * 2,
      (j - 2) * 1.5
    );

    scene.add(object);
    objects.push(object);
  }
}

// Simple camera controls using keyboard
const keys = {};
const cameraSpeed = 0.1;

const onKeyDown = (event) => {
  keys[event.code] = true;
};

const onKeyUp = (event) => {
  keys[event.code] = false;
};

// Update camera position based on keys
const updateCamera = () => {
  if (keys['KeyW']) camera.position.z -= cameraSpeed; // Forward
  if (keys['KeyS']) camera.position.z += cameraSpeed; // Backward
  if (keys['KeyA']) camera.position.x -= cameraSpeed; // Left
  if (keys['KeyD']) camera.position.x += cameraSpeed; // Right
  if (keys['KeyQ']) camera.position.y += cameraSpeed; // Up
  if (keys['KeyE']) camera.position.y -= cameraSpeed; // Down

  // Always look at the center
  camera.lookAt(0, 0, 0);
};

window.addEventListener('keydown', onKeyDown);
window.addEventListener('keyup', onKeyUp);

// Set initial camera position
camera.position.set(3, 3, 3);
camera.lookAt(0, 0, 0);

// Animation loop with camera updates
animate(() => {
  updateCamera();

  // Gentle rotation of objects
  objects.forEach((obj, index) => {
    obj.rotation.y += 0.01 * (index % 3 + 1);
  });
});

console.log('🎮 Use WASD keys to move around the scene!');
console.log('Q/E to move up/down');`,
        nextStep: "Next: Build a complete interactive scene"
      },
      {
        id: "step4-interactive-scene",
        title: "Step 4: Complete Interactive Scene",
        type: "concept",
        content: "Now let's combine everything - mouse interaction, visual feedback, and camera controls - to create a fully interactive 3D experience that responds to user input in multiple ways.",
        explanation: "A great interactive 3D scene feels alive and responsive. Users should be able to explore, interact with objects, and get immediate feedback that makes the experience feel natural and engaging.",
        codeExample: `// Create an interactive gallery scene
const artPieces = [];
const selectedObject = { current: null };

// Create art pieces with different shapes and colors
const shapes = [
  () => new THREE.BoxGeometry(1, 1, 1),
  () => new THREE.SphereGeometry(0.7, 32, 16),
  () => new THREE.CylinderGeometry(0.5, 0.5, 1, 8),
  () => new THREE.ConeGeometry(0.6, 1.2, 8)
];

for (let i = 0; i < 6; i++) {
  const geometry = shapes[i % shapes.length]();
  const material = new THREE.MeshBasicMaterial({
    color: new THREE.Color().setHSL(i / 6, 0.8, 0.6)
  });

  const artPiece = new THREE.Mesh(geometry, material);
  artPiece.position.set(
    (i % 3 - 1) * 3,
    0,
    Math.floor(i / 3) * 3 - 1.5
  );

  artPiece.userData = {
    originalColor: artPiece.material.color.getHex(),
    originalScale: 1,
    isSelected: false
  };

  scene.add(artPiece);
  artPieces.push(artPiece);
}

// Interaction setup
const raycaster = new THREE.Raycaster();
const mouse = new THREE.Vector2();

// Mouse interaction
const onMouseMove = (event) => {
  // Get canvas bounds for accurate coordinate mapping
  const canvas = renderer.domElement;
  const rect = canvas.getBoundingClientRect();

  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

  raycaster.setFromCamera(mouse, camera);
  const intersects = raycaster.intersectObjects(artPieces);

  // Reset hover states
  artPieces.forEach(piece => {
    if (!piece.userData.isSelected) {
      piece.material.color.setHex(piece.userData.originalColor);
      piece.scale.set(1, 1, 1);
    }
  });

  // Highlight hovered object
  if (intersects.length > 0) {
    const hovered = intersects[0].object;
    if (!hovered.userData.isSelected) {
      hovered.material.color.setHex(0xffffff);
      hovered.scale.set(1.1, 1.1, 1.1);
    }
  }
};

const onMouseClick = (event) => {
  raycaster.setFromCamera(mouse, camera);
  const intersects = raycaster.intersectObjects(artPieces);

  if (intersects.length > 0) {
    const clicked = intersects[0].object;

    // Deselect previous selection
    if (selectedObject.current) {
      selectedObject.current.userData.isSelected = false;
      selectedObject.current.material.color.setHex(selectedObject.current.userData.originalColor);
      selectedObject.current.scale.set(1, 1, 1);
    }

    // Select new object
    selectedObject.current = clicked;
    clicked.userData.isSelected = true;
    clicked.material.color.setHex(0xff6b6b);
    clicked.scale.set(1.3, 1.3, 1.3);

    console.log('Selected art piece!');
  }
};

window.addEventListener('mousemove', onMouseMove);
window.addEventListener('click', onMouseClick);

// Camera setup
camera.position.set(0, 3, 5);
camera.lookAt(0, 0, 0);

// Gentle animation
animate(() => {
  artPieces.forEach((piece, index) => {
    if (!piece.userData.isSelected) {
      piece.rotation.y += 0.005 * (index % 3 + 1);
    } else {
      piece.rotation.y += 0.02; // Selected object spins faster
    }
  });
});

console.log('🎨 Interactive Art Gallery');
console.log('Hover to preview, click to select art pieces!');`,
        nextStep: "🎉 You can now create fully interactive 3D experiences!"
      }
    ],
    initialCode: `// 🎯 Interactive 3D Scene - Click and Explore!

// Create clickable objects
const objects = [];
const colors = [0xff6b6b, 0x4ecdc4, 0xf39c12, 0x9b59b6];

for (let i = 0; i < 4; i++) {
  const object = new THREE.Mesh(
    new THREE.BoxGeometry(1, 1, 1),
    new THREE.MeshBasicMaterial({ color: colors[i] })
  );
  object.position.x = (i - 1.5) * 2;
  object.userData = {
    originalColor: colors[i],
    clickCount: 0
  };
  scene.add(object);
  objects.push(object);
}

// Set up raycasting for mouse interaction
const raycaster = new THREE.Raycaster();
const mouse = new THREE.Vector2();

// Mouse click detection
const onMouseClick = (event) => {
  // Get canvas bounds for accurate coordinate mapping
  const canvas = renderer.domElement;
  const rect = canvas.getBoundingClientRect();

  // Convert mouse position to normalized coordinates
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

  // Cast ray from camera through mouse position
  raycaster.setFromCamera(mouse, camera);

  // Check for intersections
  const intersects = raycaster.intersectObjects(objects);

  if (intersects.length > 0) {
    const clickedObject = intersects[0].object;
    clickedObject.userData.clickCount++;

    // Spin the object
    clickedObject.rotation.y += Math.PI / 2;

    // Change color based on click count
    const hue = (clickedObject.userData.clickCount * 60) % 360;
    clickedObject.material.color.setHSL(hue / 360, 0.8, 0.6);

    console.log(\`Clicked object \${clickedObject.userData.clickCount} times!\`);
  }
};

// Add event listener
window.addEventListener('click', onMouseClick);

camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

console.log('🖱️ Click on the cubes to interact with them!');
console.log('Each click spins the cube and changes its color');

// Try experimenting with:
// - Different object shapes and sizes
// - Hover effects using 'mousemove' events
// - Keyboard controls for camera movement`,
    tasks: [
      {
        title: "🎮 Interactive Game",
        description: "Create a simple clicking game where objects appear randomly and users must click them before they disappear",
        hint: "Use setTimeout to make objects disappear, and track score when users click them in time"
      },
      {
        title: "🎨 Color Picker",
        description: "Create a 3D color picker where clicking on different objects changes the color of a central object",
        hint: "Use raycasting to detect clicks on color swatches, then apply those colors to a target object"
      },
      {
        title: "🏗️ 3D Builder",
        description: "Let users click to place objects in 3D space, building their own scene",
        hint: "Use raycasting to find where the mouse intersects with a ground plane, then place objects at that position"
      }
    ],
    examples: [
      {
        title: "Raycasting Setup Pattern",
        description: "Standard setup for mouse interaction in Three.js",
        code: `// Standard raycasting setup
const raycaster = new THREE.Raycaster();
const mouse = new THREE.Vector2();

const onMouseClick = (event) => {
  // Get canvas bounds for accurate coordinate mapping
  const canvas = renderer.domElement;
  const rect = canvas.getBoundingClientRect();

  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
  raycaster.setFromCamera(mouse, camera);
  const intersects = raycaster.intersectObjects(clickableObjects);
  if (intersects.length > 0) {
    // Handle click on intersects[0].object
  }
};

window.addEventListener('click', onMouseClick);`
      }
    ]
  },

  8: {
    id: 8,
    title: "Asset Loading & Management",
    difficulty: "Intermediate",
    category: "Assets",
    description: "Learn how to load complex 3D models, textures, and animations into your scenes! Discover how to manage loading times and create smooth user experiences.",
    objectives: [
      "Load external 3D models and textures",
      "Handle loading progress and errors gracefully",
      "Understand different 3D file formats",
      "Manage asset performance and optimization"
    ],
    theory: [
      "3D assets like models, textures, and animations are usually too complex to create in code - they're made in 3D software and loaded into your scene.",
      "Loading assets takes time, especially over the internet, so good loading strategies keep users engaged while content loads.",
      "Different file formats serve different purposes - some are optimized for web delivery, others for maximum quality or compatibility.",
      "Asset management involves balancing quality, file size, and loading speed to create the best user experience."
    ],
    concepts: [
      { name: "3D File Formats", description: "GLTF, OBJ, FBX - each with different strengths", example: "GLTF: web-optimized, OBJ: simple geometry, FBX: complex animations" },
      { name: "Loading Strategies", description: "Progressive loading, preloading, lazy loading", example: "Show low-res first, then upgrade to high-res" },
      { name: "Progress Feedback", description: "Loading bars, spinners, percentage indicators", example: "Keep users informed about loading progress" },
      { name: "Error Handling", description: "Graceful fallbacks when assets fail to load", example: "Show placeholder objects or retry mechanisms" }
    ],
    learningPath: [
      {
        id: "step1-loading-textures",
        title: "Step 1: Loading Textures",
        type: "concept",
        content: "Textures are images that we wrap around 3D objects to give them realistic appearance. Learning to load external texture files is the first step in asset management.",
        explanation: "Instead of creating textures in code, we usually load them from image files. This lets artists create detailed textures in image editing software and gives us much more realistic results.",
        codeExample: `// Create a texture loader
const textureLoader = new THREE.TextureLoader();

// Create objects to apply textures to
const geometry = new THREE.BoxGeometry(2, 2, 2);

// Load a simple procedural texture
const canvas = document.createElement('canvas');
canvas.width = 256;
canvas.height = 256;
const context = canvas.getContext('2d');

// Create a brick pattern
context.fillStyle = '#8B4513'; // Brown
context.fillRect(0, 0, 256, 256);

context.fillStyle = '#A0522D'; // Lighter brown
for (let y = 0; y < 256; y += 32) {
  for (let x = 0; x < 256; x += 64) {
    const offsetX = (y / 32) % 2 === 0 ? 0 : 32;
    context.fillRect(x + offsetX, y, 60, 28);
  }
}

// Create texture from canvas
const brickTexture = new THREE.CanvasTexture(canvas);
brickTexture.wrapS = THREE.RepeatWrapping;
brickTexture.wrapT = THREE.RepeatWrapping;
brickTexture.repeat.set(2, 2);

// Apply texture to material
const texturedCube = new THREE.Mesh(
  geometry,
  new THREE.MeshBasicMaterial({ map: brickTexture })
);

scene.add(texturedCube);
camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

// Animate to see the texture
animate(() => {
  texturedCube.rotation.y += 0.01;
  texturedCube.rotation.x += 0.005;
});

console.log('🧱 Procedural brick texture loaded and applied');
console.log('In real projects, you would load from image files');`,
        nextStep: "Next: Handle loading progress and errors"
      },
      {
        id: "step2-loading-progress",
        title: "Step 2: Loading Progress",
        type: "concept",
        content: "When loading large assets, it's important to show users the progress so they know something is happening. Good loading feedback keeps users engaged and prevents them from leaving.",
        explanation: "Think of downloading a large file - you want to see a progress bar so you know it's working and how much longer it will take. 3D asset loading works the same way.",
        codeExample: `// Create a loading manager to track progress
const loadingManager = new THREE.LoadingManager();
const textureLoader = new THREE.TextureLoader(loadingManager);

// Track loading progress
loadingManager.onStart = (url, itemsLoaded, itemsTotal) => {
  console.log(\`Started loading: \${url}\`);
  console.log(\`Items loaded: \${itemsLoaded} of \${itemsTotal}\`);
};

loadingManager.onProgress = (url, itemsLoaded, itemsTotal) => {
  const progress = (itemsLoaded / itemsTotal) * 100;
  console.log(\`Loading progress: \${progress.toFixed(1)}%\`);

  // In a real app, you would update a progress bar here
  // progressBar.style.width = progress + '%';
};

loadingManager.onLoad = () => {
  console.log('✅ All assets loaded successfully!');
  // Hide loading screen, start the experience
};

loadingManager.onError = (url) => {
  console.error(\`❌ Error loading: \${url}\`);
  // Show error message, provide fallback
};

// Simulate loading multiple assets
const assets = [];
const colors = [0xff6b6b, 0x4ecdc4, 0xf39c12];

for (let i = 0; i < 3; i++) {
  // Create a simple texture for each object
  const canvas = document.createElement('canvas');
  canvas.width = 128;
  canvas.height = 128;
  const context = canvas.getContext('2d');

  // Fill with color and add some pattern
  context.fillStyle = \`#\${colors[i].toString(16).padStart(6, '0')}\`;
  context.fillRect(0, 0, 128, 128);

  context.fillStyle = 'white';
  context.font = '20px Arial';
  context.fillText(\`Asset \${i + 1}\`, 30, 70);

  const texture = new THREE.CanvasTexture(canvas);

  const cube = new THREE.Mesh(
    new THREE.BoxGeometry(1, 1, 1),
    new THREE.MeshBasicMaterial({ map: texture })
  );
  cube.position.x = (i - 1) * 2;

  scene.add(cube);
  assets.push(cube);
}

camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

// Animate the loaded assets
animate(() => {
  assets.forEach((asset, index) => {
    asset.rotation.y += 0.01 * (index + 1);
  });
});

console.log('📊 Loading manager tracks asset loading progress');`,
        nextStep: "Next: Learn about 3D model formats"
      },
      {
        id: "step3-model-formats",
        title: "Step 3: 3D Model Formats",
        type: "concept",
        content: "Different **3D file formats** serve different purposes! 📁 Choosing the right format dramatically affects performance, file size, and capabilities.\n\n🚀 **GLTF (.gltf/.glb) - WEB OPTIMIZED:**\n🌐 **Designed for web** - fast loading, efficient rendering\n📦 **Binary version (.glb)** - single file with everything included\n✨ **Supports** - materials, textures, animations, lighting\n⚡ **Performance** - optimized for real-time rendering\n🏆 **Best for** - web applications, games, interactive experiences\n💻 `new THREE.GLTFLoader().load('model.glb')`\n\n📐 **OBJ (.obj) - SIMPLE & UNIVERSAL:**\n🔧 **Simple format** - widely supported across all 3D software\n📝 **Text-based** - human-readable, easy to debug\n⚠️ **Limitations** - no materials, animations, or advanced features\n📦 **Often paired** with .mtl files for materials\n🎯 **Best for** - simple geometry, static models\n💻 `new THREE.OBJLoader().load('model.obj')`\n\n🎬 **FBX (.fbx) - ANIMATION POWERHOUSE:**\n🎭 **Complex animations** - skeletal animation, morphing\n🏢 **Industry standard** - used by major studios\n📊 **Rich data** - cameras, lights, materials, textures\n💾 **Large files** - not optimized for web delivery\n🎯 **Best for** - complex animated characters, studio workflows\n💻 `new THREE.FBXLoader().load('character.fbx')`\n\n🎨 **COLLADA (.dae) - OPEN STANDARD:**\n🌐 **Open format** - XML-based, vendor-neutral\n🔄 **Good compatibility** - supported by most 3D software\n📊 **Comprehensive** - geometry, materials, animations, physics\n⚠️ **Verbose** - XML structure creates larger files\n🎯 **Best for** - cross-platform compatibility, archival\n💻 `new THREE.ColladaLoader().load('scene.dae')`\n\n🎯 **CHOOSING THE RIGHT FORMAT:**\n🌐 **Web projects** → GLTF/GLB (optimized for browsers)\n📐 **Simple geometry** → OBJ (lightweight, universal)\n🎬 **Complex animation** → FBX (industry standard)\n🔄 **Cross-platform** → COLLADA (open standard)\n⚡ **Performance priority** → GLTF (fastest loading)\n💻 `// Choose based on your specific needs`\n\n📊 **FILE SIZE COMPARISON:**\n🏆 **GLTF/GLB** - smallest, most efficient\n📐 **OBJ** - small for geometry only\n📦 **FBX** - larger, includes everything\n📄 **COLLADA** - largest due to XML structure\n💡 **Tip** - always compress files for web delivery\n💻 `// Use .glb for best web performance`",
        explanation: "Think of 3D file formats like different types of image files - JPEG is small and fast, PNG supports transparency, and RAW has maximum quality. Each 3D format has its own strengths and use cases.",
        codeExample: `// Demonstrate different approaches to 3D content

// Method 1: Procedural geometry (created in code)
const proceduralGeometry = new THREE.SphereGeometry(1, 32, 16);
const proceduralMaterial = new THREE.MeshBasicMaterial({
  color: 0xff6b6b,
  wireframe: true
});
const proceduralSphere = new THREE.Mesh(proceduralGeometry, proceduralMaterial);
proceduralSphere.position.x = -3;
scene.add(proceduralSphere);

// Method 2: Simple geometry with complex materials
const simpleGeometry = new THREE.BoxGeometry(1, 1, 1);
const complexMaterial = new THREE.MeshStandardMaterial({
  color: 0x4ecdc4,
  metalness: 0.7,
  roughness: 0.3
});
const materialCube = new THREE.Mesh(simpleGeometry, complexMaterial);
materialCube.position.x = 0;
scene.add(materialCube);

// Method 3: Complex geometry (simulated loaded model)
const complexGeometry = new THREE.CylinderGeometry(0.5, 0.8, 1.5, 8);
const loadedMaterial = new THREE.MeshBasicMaterial({ color: 0xf39c12 });
const loadedModel = new THREE.Mesh(complexGeometry, loadedMaterial);
loadedModel.position.x = 3;
scene.add(loadedModel);

// Add lighting for the standard material
const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
directionalLight.position.set(5, 5, 5);
scene.add(ambientLight, directionalLight);

camera.position.set(0, 2, 8);
camera.lookAt(0, 0, 0);

// Animate to show different approaches
animate(() => {
  proceduralSphere.rotation.y += 0.02;
  materialCube.rotation.x += 0.01;
  materialCube.rotation.y += 0.01;
  loadedModel.rotation.z += 0.015;
});

console.log('🎭 Different approaches to 3D content:');
console.log('Left: Procedural (code), Middle: Material-focused, Right: Model-focused');

// Format comparison:
console.log('📁 3D File Format Guide:');
console.log('GLTF (.gltf/.glb): Web-optimized, animations, PBR materials');
console.log('OBJ (.obj): Simple geometry, widely supported, no animations');
console.log('FBX (.fbx): Complex animations, industry standard, large files');
console.log('STL (.stl): 3D printing, geometry only, no materials');`,
        nextStep: "Next: Optimize loading performance"
      },
      {
        id: "step4-optimization",
        title: "Step 4: Loading Optimization",
        type: "concept",
        content: "Smart loading strategies can dramatically improve user experience. When users visit your 3D website, they don't want to wait 30 seconds staring at a blank screen. Progressive loading shows something immediately, then upgrades the quality as more data arrives. This keeps users engaged and prevents them from leaving.\n\nLevel of Detail (LOD) is a key optimization technique - imagine you're looking at a forest. Trees close to you need to show individual leaves and bark texture, but trees far away can just be simple green shapes. Your brain fills in the details it expects to see. LOD systems work the same way - they automatically switch between high-detail models when objects are close and low-detail models when they're far away.\n\nCompression is like packing a suitcase efficiently. A raw 3D model might be 50MB, but with smart compression, you can get it down to 5MB without losing visual quality. GLTF format is specifically designed for web delivery and includes built-in compression.\n\nCaching means storing frequently used assets locally so they don't need to be downloaded again. It's like keeping your favorite books on your desk instead of walking to the library every time you need them.",
        explanation: "Think of loading optimization like running a restaurant. You want to serve customers quickly, so you prep ingredients ahead of time (preloading), keep popular items ready (caching), and adjust portion sizes based on how hungry people are (adaptive quality). The goal is to give users a great experience without making them wait.",
        codeExample: `// Demonstrate progressive loading and optimization techniques

// Level of Detail (LOD) system - show simple first, detailed later
const createLODObject = (position, color) => {
  const group = new THREE.Group();

  // Low detail version (loads first)
  const lowDetailGeometry = new THREE.BoxGeometry(1, 1, 1);
  const lowDetailMaterial = new THREE.MeshBasicMaterial({
    color: color,
    transparent: true,
    opacity: 0.7
  });
  const lowDetail = new THREE.Mesh(lowDetailGeometry, lowDetailMaterial);
  group.add(lowDetail);

  // High detail version (loads after delay)
  setTimeout(() => {
    const highDetailGeometry = new THREE.SphereGeometry(0.7, 32, 16);
    const highDetailMaterial = new THREE.MeshStandardMaterial({
      color: color,
      metalness: 0.5,
      roughness: 0.3
    });
    const highDetail = new THREE.Mesh(highDetailGeometry, highDetailMaterial);

    // Fade out low detail, fade in high detail
    const fadeTransition = () => {
      lowDetailMaterial.opacity -= 0.02;
      highDetailMaterial.opacity = Math.min(1, highDetailMaterial.opacity + 0.02);

      if (lowDetailMaterial.opacity <= 0) {
        group.remove(lowDetail);
        console.log('Upgraded to high detail model');
      } else {
        requestAnimationFrame(fadeTransition);
      }
    };

    highDetailMaterial.transparent = true;
    highDetailMaterial.opacity = 0;
    group.add(highDetail);
    fadeTransition();

  }, 2000); // Simulate network delay

  group.position.copy(position);
  return group;
};

// Create progressive loading objects
const object1 = createLODObject(new THREE.Vector3(-2, 0, 0), 0xff6b6b);
const object2 = createLODObject(new THREE.Vector3(0, 0, 0), 0x4ecdc4);
const object3 = createLODObject(new THREE.Vector3(2, 0, 0), 0xf39c12);

scene.add(object1, object2, object3);

// Add lighting for high-detail materials
const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
directionalLight.position.set(5, 5, 5);
scene.add(ambientLight, directionalLight);

camera.position.set(0, 2, 6);
camera.lookAt(0, 0, 0);

// Animate the objects
animate(() => {
  object1.rotation.y += 0.01;
  object2.rotation.y += 0.015;
  object3.rotation.y += 0.02;
});

console.log('⚡ Progressive loading demonstration:');
console.log('1. Low-detail models appear immediately');
console.log('2. High-detail models load and replace them');
console.log('3. Smooth transition maintains user engagement');

// Optimization tips
console.log('🚀 Loading Optimization Tips:');
console.log('• Use compressed textures (JPEG for photos, PNG for graphics)');
console.log('• Implement Level of Detail (LOD) systems');
console.log('• Cache frequently used assets');
console.log('• Load critical assets first, optional ones later');
console.log('• Show placeholders while loading');`,
        nextStep: "🎉 You can now manage 3D assets like a pro!"
      }
    ],
    initialCode: `// 📦 Asset Loading & Management Demo

// Create a loading manager to track progress
const loadingManager = new THREE.LoadingManager();

// Track loading events
loadingManager.onStart = (url, itemsLoaded, itemsTotal) => {
  console.log(\`📥 Started loading assets...\`);
};

loadingManager.onProgress = (url, itemsLoaded, itemsTotal) => {
  const progress = (itemsLoaded / itemsTotal) * 100;
  console.log(\`📊 Loading progress: \${progress.toFixed(1)}%\`);
};

loadingManager.onLoad = () => {
  console.log('✅ All assets loaded successfully!');
};

// Create textured objects to simulate loaded assets
const assets = [];
const assetNames = ['Wood', 'Metal', 'Fabric'];
const colors = [0x8B4513, 0x708090, 0x4169E1];

for (let i = 0; i < 3; i++) {
  // Create a simple texture
  const canvas = document.createElement('canvas');
  canvas.width = 128;
  canvas.height = 128;
  const context = canvas.getContext('2d');

  // Base color
  context.fillStyle = \`#\${colors[i].toString(16).padStart(6, '0')}\`;
  context.fillRect(0, 0, 128, 128);

  // Add texture pattern
  context.fillStyle = 'rgba(255, 255, 255, 0.1)';
  for (let x = 0; x < 128; x += 8) {
    for (let y = 0; y < 128; y += 8) {
      if ((x + y) % 16 === 0) {
        context.fillRect(x, y, 4, 4);
      }
    }
  }

  // Add label
  context.fillStyle = 'white';
  context.font = '16px Arial';
  context.fillText(assetNames[i], 20, 70);

  const texture = new THREE.CanvasTexture(canvas);

  // Create object with texture
  const object = new THREE.Mesh(
    new THREE.BoxGeometry(1.5, 1.5, 1.5),
    new THREE.MeshBasicMaterial({ map: texture })
  );
  object.position.x = (i - 1) * 3;

  scene.add(object);
  assets.push(object);
}

camera.position.set(0, 2, 6);
camera.lookAt(0, 0, 0);

// Animate the loaded assets
animate(() => {
  assets.forEach((asset, index) => {
    asset.rotation.y += 0.01 * (index + 1);
    asset.rotation.x += 0.005;
  });
});

console.log('🎨 Simulated asset loading with textures');
console.log('💡 In real projects, use GLTFLoader for 3D models');
console.log('📁 Popular formats: GLTF (web), OBJ (simple), FBX (complex)');

// Asset management tips:
console.log('⚡ Optimization tips:');
console.log('• Compress textures and models');
console.log('• Use progressive loading (low-res → high-res)');
console.log('• Cache frequently used assets');
console.log('• Show loading progress to users');`,
    tasks: [
      {
        title: "📊 Loading Screen",
        description: "Create a loading screen that shows progress while assets load, then fades out when complete",
        hint: "Use LoadingManager events to track progress and update a visual progress bar"
      },
      {
        title: "🎭 Asset Gallery",
        description: "Create a gallery where users can click buttons to load and display different 3D models",
        hint: "Use buttons to trigger loading of different assets, show loading states for each"
      },
      {
        title: "⚡ Performance Optimizer",
        description: "Implement a Level of Detail (LOD) system that shows simple models when far away, detailed when close",
        hint: "Calculate distance from camera to objects and swap between high/low detail versions"
      }
    ],
    examples: [
      {
        title: "Loading Manager Setup",
        description: "Standard pattern for tracking asset loading",
        code: `const loadingManager = new THREE.LoadingManager();
const loader = new THREE.GLTFLoader(loadingManager);

loadingManager.onProgress = (url, loaded, total) => {
  const progress = (loaded / total) * 100;
  updateProgressBar(progress);
};

loadingManager.onLoad = () => {
  hideLoadingScreen();
  startExperience();
};

loader.load('model.gltf', (gltf) => {
  scene.add(gltf.scene);
});`
      }
    ]
  },

  9: {
    id: 9,
    title: "Post-Processing & Effects",
    difficulty: "Advanced",
    category: "Effects",
    description: "Add cinematic effects to your 3D scenes! Learn how to apply filters, glows, and other visual effects that make your renders look professional and polished.",
    objectives: [
      "Apply visual effects to enhance scene appearance",
      "Understand how post-processing works",
      "Create cinematic and stylistic effects",
      "Balance visual quality with performance"
    ],
    theory: [
      "Post-processing effects are like Instagram filters for 3D scenes - they're applied after the scene is rendered to enhance the final image.",
      "Effects work by taking the rendered image and applying mathematical transformations to create glows, blurs, color adjustments, and other visual enhancements.",
      "Layering multiple effects creates complex, cinematic looks that can dramatically change the mood and style of your 3D scenes.",
      "Each effect has a performance cost, so balancing visual impact with frame rate is important for smooth experiences."
    ],
    concepts: [
      { name: "Post-Processing Pipeline", description: "Effects applied after scene rendering", example: "Like photo filters applied to a finished photograph" },
      { name: "Visual Effects", description: "Bloom, blur, color grading, vignette", example: "Glow effects, depth of field, mood lighting" },
      { name: "Render Passes", description: "Multiple rendering stages for complex effects", example: "Scene → Depth → Blur → Combine → Final" },
      { name: "Performance Balance", description: "Quality vs speed trade-offs", example: "High-quality effects vs smooth frame rates" }
    ],
    learningPath: [
      {
        id: "step1-basic-effects",
        title: "Step 1: Basic Color Effects",
        type: "concept",
        content: "**Post-processing** starts with simple color adjustments that can dramatically transform your scene's mood! 🎨 Small changes create powerful emotional impact.\n\n🎨 **BASIC COLOR ADJUSTMENTS:**\n☀️ **Brightness** - makes scenes lighter or darker overall\n⚖️ **Contrast** - increases or decreases difference between light and dark\n🌈 **Saturation** - controls color intensity (vivid vs washed out)\n🎭 **Hue** - shifts entire color palette (warmer vs cooler)\n💻 `new THREE.ShaderPass(brightnessShader)`\n\n🎬 **MOOD TRANSFORMATION:**\n🌅 **Warm colors** (orange/yellow) → cozy, comfortable, inviting\n❄️ **Cool colors** (blue/purple) → cold, dramatic, mysterious\n🔆 **High brightness** → cheerful, energetic, optimistic\n🌑 **Low brightness** → moody, serious, cinematic\n💻 `colorPass.uniforms.brightness.value = 0.2` // darker mood\n\n📺 **LIKE TV/CAMERA SETTINGS:**\n📱 **Phone camera filters** - Instagram-style color grading\n📺 **TV picture modes** - vivid, cinema, natural\n🎥 **Movie color grading** - orange/teal, desaturated, high contrast\n🎯 **Same principle** - small adjustments, big visual impact\n💻 `saturationPass.uniforms.saturation.value = 1.5` // more vivid\n\n🔧 **TECHNICAL IMPLEMENTATION:**\n🖼️ **Render to texture** - scene rendered to off-screen buffer\n🎨 **Apply shader** - color adjustment calculations\n📺 **Display result** - processed image shown on screen\n⚡ **GPU accelerated** - real-time performance\n💻 `composer.addPass(colorCorrectionPass)`\n\n🎯 **PRACTICAL APPLICATIONS:**\n🎮 **Game moods** - health low = desaturated, power-up = bright\n🏠 **Architectural viz** - warm lighting for cozy homes\n🎬 **Storytelling** - color supports narrative emotion\n📊 **Data viz** - highlight important information with color\n💻 `// Dynamic mood: if(playerHealth < 20) brightness = 0.3`\n\n⚡ **PERFORMANCE BENEFITS:**\n🚀 **Lightweight** - basic color ops are very fast\n🔄 **Real-time** - can be adjusted dynamically\n📱 **Mobile friendly** - works well on all devices\n🎯 **Foundation** - building block for complex effects\n💻 `// Start simple, add complexity gradually`",
        explanation: "Think of these effects like adjusting the settings on your TV or phone camera. Small changes in color and brightness can make a scene feel warm and cozy or cold and dramatic.",
        codeExample: `// Create a scene to apply effects to
const objects = [];

// Create colorful objects
for (let i = 0; i < 5; i++) {
  const geometry = new THREE.SphereGeometry(0.5, 32, 16);
  const material = new THREE.MeshBasicMaterial({
    color: new THREE.Color().setHSL(i / 5, 0.8, 0.6)
  });
  const sphere = new THREE.Mesh(geometry, material);
  sphere.position.x = (i - 2) * 1.5;
  sphere.position.y = Math.sin(i) * 0.5;
  scene.add(sphere);
  objects.push(sphere);
}

// Add lighting
const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
directionalLight.position.set(5, 5, 5);
scene.add(ambientLight, directionalLight);

camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

// Simulate different color effects with animation
let effectTime = 0;

animate(() => {
  effectTime += 0.02;

  // Rotate objects
  objects.forEach((obj, index) => {
    obj.rotation.y += 0.01 * (index + 1);
  });

  // Simulate color effects by changing scene background
  const hue = (Math.sin(effectTime * 0.5) + 1) * 0.5; // 0 to 1
  const saturation = 0.3 + (Math.sin(effectTime * 0.3) + 1) * 0.2; // 0.3 to 0.7
  const lightness = 0.1 + (Math.sin(effectTime * 0.7) + 1) * 0.1; // 0.1 to 0.3

  scene.background = new THREE.Color().setHSL(hue, saturation, lightness);

  // Adjust lighting intensity for mood
  const intensity = 0.5 + (Math.sin(effectTime * 0.4) + 1) * 0.3;
  directionalLight.intensity = intensity;
});

console.log('🎨 Color effects change the mood of your scene');
console.log('Watch how background color and lighting create different atmospheres');

// Effect descriptions
console.log('🎭 Common color effects:');
console.log('• Brightness: Makes everything lighter or darker');
console.log('• Contrast: Increases difference between light and dark');
console.log('• Saturation: Makes colors more or less vivid');
console.log('• Hue shift: Changes the overall color tone');`,
        nextStep: "Next: Create glow and bloom effects"
      },
      {
        id: "step2-glow-effects",
        title: "Step 2: Glow and Bloom Effects",
        type: "concept",
        content: "Glow and bloom effects make bright objects appear to emit light, creating a magical, ethereal quality. These effects are perfect for creating sci-fi interfaces, magical objects, or dramatic lighting.",
        explanation: "Think of how a bright light bulb seems to glow and create halos around it, or how the sun creates lens flares in photos. Bloom effects simulate this natural light behavior in 3D scenes.",
        codeExample: `// Create glowing objects
const glowingObjects = [];

// Create objects with emissive materials (self-illuminating)
const colors = [0xff0040, 0x40ff00, 0x0040ff, 0xff8000, 0x8000ff];

for (let i = 0; i < 5; i++) {
  const geometry = new THREE.SphereGeometry(0.4, 32, 16);
  const material = new THREE.MeshBasicMaterial({
    color: colors[i],
    transparent: true,
    opacity: 0.8
  });

  const glowingSphere = new THREE.Mesh(geometry, material);
  glowingSphere.position.x = (i - 2) * 2;
  glowingSphere.position.y = Math.sin(i * 2) * 1;

  scene.add(glowingSphere);
  glowingObjects.push(glowingSphere);

  // Add a larger, more transparent sphere for glow effect
  const glowGeometry = new THREE.SphereGeometry(0.6, 16, 16);
  const glowMaterial = new THREE.MeshBasicMaterial({
    color: colors[i],
    transparent: true,
    opacity: 0.2,
    side: THREE.BackSide // Render from inside
  });

  const glowEffect = new THREE.Mesh(glowGeometry, glowMaterial);
  glowingSphere.add(glowEffect); // Attach to main sphere
}

camera.position.set(0, 2, 8);
camera.lookAt(0, 0, 0);

// Animate the glow intensity
animate(() => {
  const time = Date.now() * 0.001;

  glowingObjects.forEach((obj, index) => {
    // Rotate objects
    obj.rotation.y += 0.02;

    // Pulse the glow effect
    const pulse = 0.5 + Math.sin(time * 2 + index) * 0.3;
    obj.material.opacity = pulse;

    // Animate the outer glow
    const glowEffect = obj.children[0];
    if (glowEffect) {
      glowEffect.material.opacity = pulse * 0.3;
      glowEffect.scale.setScalar(1 + pulse * 0.5);
    }

    // Float up and down
    obj.position.y = Math.sin(time + index * 2) * 1.5;
  });
});

console.log('✨ Simulated glow and bloom effects');
console.log('Real bloom requires post-processing passes');
console.log('This demo shows the visual concept using layered materials');

// Bloom effect explanation
console.log('🌟 How bloom works:');
console.log('1. Render scene normally');
console.log('2. Extract bright areas');
console.log('3. Blur the bright areas');
console.log('4. Combine with original scene');`,
        nextStep: "Next: Add depth and focus effects"
      },
      {
        id: "step3-depth-effects",
        title: "Step 3: Depth of Field",
        type: "concept",
        content: "Depth of field effects blur objects that are far from the camera's focus point, just like how a camera lens works. This creates a cinematic look and draws attention to specific objects.",
        explanation: "When you take a photo with a camera, objects in focus are sharp while objects in the background or foreground are blurry. This natural effect helps direct the viewer's attention and creates a professional, cinematic look.",
        codeExample: `// Create a scene with objects at different depths
const focusObjects = [];

// Create objects at different distances from camera
for (let i = 0; i < 10; i++) {
  const geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5);
  const material = new THREE.MeshBasicMaterial({
    color: new THREE.Color().setHSL(i / 10, 0.8, 0.6)
  });

  const cube = new THREE.Mesh(geometry, material);
  cube.position.z = -i * 2; // Spread objects in depth
  cube.position.x = (Math.random() - 0.5) * 4;
  cube.position.y = (Math.random() - 0.5) * 2;

  scene.add(cube);
  focusObjects.push(cube);
}

// Focus point (where the camera is looking)
const focusDistance = 5; // Distance to focus point
const focusIndicator = new THREE.Mesh(
  new THREE.SphereGeometry(0.2, 16, 16),
  new THREE.MeshBasicMaterial({ color: 0xff0000 })
);
focusIndicator.position.z = -focusDistance;
scene.add(focusIndicator);

camera.position.set(0, 1, 3);
camera.lookAt(0, 0, -focusDistance);

// Simulate depth of field by adjusting opacity based on distance from focus
animate(() => {
  focusObjects.forEach((obj, index) => {
    // Rotate objects
    obj.rotation.x += 0.01;
    obj.rotation.y += 0.02;

    // Calculate distance from focus point
    const distanceFromFocus = Math.abs(obj.position.z + focusDistance);

    // Objects far from focus become more transparent (simulating blur)
    const maxBlurDistance = 3;
    const blurAmount = Math.min(distanceFromFocus / maxBlurDistance, 1);
    obj.material.opacity = 1 - blurAmount * 0.7; // Keep some visibility
    obj.material.transparent = true;

    // Also scale objects slightly to enhance the effect
    const scale = 1 - blurAmount * 0.3;
    obj.scale.setScalar(scale);
  });

  // Animate focus point
  const time = Date.now() * 0.001;
  focusIndicator.position.z = -focusDistance + Math.sin(time) * 2;
});

console.log('📷 Simulated depth of field effect');
console.log('Red sphere shows the focus point');
console.log('Objects far from focus become blurred (transparent)');

// Depth of field explanation
console.log('🎯 Depth of field creates:');
console.log('• Cinematic, professional look');
console.log('• Focus attention on important objects');
console.log('• Sense of depth and scale');
console.log('• Artistic, photographic quality');`,
        nextStep: "Next: Combine multiple effects"
      },
      {
        id: "step4-combined-effects",
        title: "Step 4: Cinematic Combinations",
        type: "concept",
        content: "The most impressive visuals come from combining multiple effects - color grading, glow, depth of field, and atmospheric effects working together to create a cohesive, cinematic experience.",
        explanation: "Professional films and games use dozens of effects layered together. Each effect contributes to the overall mood and style, creating visuals that are much more than the sum of their parts.",
        codeExample: `// Create a cinematic scene with multiple effects
const cinematicObjects = [];

// Create a dramatic scene
const centerPiece = new THREE.Mesh(
  new THREE.SphereGeometry(1, 32, 16),
  new THREE.MeshBasicMaterial({
    color: 0xff6b6b,
    transparent: true,
    opacity: 0.9
  })
);
centerPiece.position.y = 1;
scene.add(centerPiece);

// Add orbiting objects
for (let i = 0; i < 8; i++) {
  const satellite = new THREE.Mesh(
    new THREE.BoxGeometry(0.3, 0.3, 0.3),
    new THREE.MeshBasicMaterial({
      color: new THREE.Color().setHSL(i / 8, 0.8, 0.7)
    })
  );

  const angle = (i / 8) * Math.PI * 2;
  satellite.position.x = Math.cos(angle) * 3;
  satellite.position.z = Math.sin(angle) * 3;
  satellite.position.y = Math.sin(i) * 0.5;

  scene.add(satellite);
  cinematicObjects.push(satellite);
}

// Add atmospheric particles
const particles = [];
for (let i = 0; i < 50; i++) {
  const particle = new THREE.Mesh(
    new THREE.SphereGeometry(0.02, 8, 8),
    new THREE.MeshBasicMaterial({
      color: 0x4ecdc4,
      transparent: true,
      opacity: 0.6
    })
  );

  particle.position.set(
    (Math.random() - 0.5) * 10,
    (Math.random() - 0.5) * 6,
    (Math.random() - 0.5) * 10
  );

  scene.add(particle);
  particles.push(particle);
}

camera.position.set(0, 3, 6);
camera.lookAt(0, 1, 0);

// Cinematic animation with multiple effects
let cinematicTime = 0;

animate(() => {
  cinematicTime += 0.02;

  // 1. Color grading effect - change overall mood
  const moodHue = (Math.sin(cinematicTime * 0.1) + 1) * 0.5;
  scene.background = new THREE.Color().setHSL(moodHue * 0.3, 0.4, 0.1);

  // 2. Central object glow effect
  const glowIntensity = 0.7 + Math.sin(cinematicTime * 2) * 0.3;
  centerPiece.material.opacity = glowIntensity;
  centerPiece.scale.setScalar(1 + Math.sin(cinematicTime * 3) * 0.1);

  // 3. Orbiting objects with depth-based effects
  cinematicObjects.forEach((obj, index) => {
    const orbitAngle = cinematicTime * 0.5 + (index / 8) * Math.PI * 2;
    obj.position.x = Math.cos(orbitAngle) * 3;
    obj.position.z = Math.sin(orbitAngle) * 3;
    obj.rotation.y += 0.05;

    // Distance-based opacity (depth of field simulation)
    const distance = obj.position.distanceTo(camera.position);
    obj.material.opacity = Math.max(0.3, 1 - (distance - 4) * 0.1);
    obj.material.transparent = true;
  });

  // 4. Atmospheric particles
  particles.forEach((particle, index) => {
    particle.position.y += Math.sin(cinematicTime + index) * 0.01;
    particle.rotation.y += 0.02;

    // Fade particles based on distance
    const distance = particle.position.distanceTo(centerPiece.position);
    particle.material.opacity = Math.max(0.1, 1 - distance * 0.1);
  });

  // 5. Camera movement for cinematic feel
  camera.position.x = Math.sin(cinematicTime * 0.3) * 2;
  camera.position.y = 3 + Math.sin(cinematicTime * 0.2) * 0.5;
  camera.lookAt(0, 1, 0);
});

console.log('🎬 Cinematic effects combination:');
console.log('• Color grading (mood lighting)');
console.log('• Glow effects (central object)');
console.log('• Depth of field (distance-based blur)');
console.log('• Atmospheric particles');
console.log('• Dynamic camera movement');`,
        nextStep: "🎉 You can now create cinematic 3D experiences!"
      }
    ],
    initialCode: `// 🎬 Post-Processing Effects Demo

// Create a scene with dramatic lighting
const effectObjects = [];

// Central glowing object
const centerSphere = new THREE.Mesh(
  new THREE.SphereGeometry(1, 32, 16),
  new THREE.MeshBasicMaterial({
    color: 0xff6b6b,
    transparent: true,
    opacity: 0.8
  })
);
scene.add(centerSphere);

// Surrounding objects for depth
for (let i = 0; i < 6; i++) {
  const object = new THREE.Mesh(
    new THREE.BoxGeometry(0.5, 0.5, 0.5),
    new THREE.MeshBasicMaterial({
      color: new THREE.Color().setHSL(i / 6, 0.8, 0.6)
    })
  );

  const angle = (i / 6) * Math.PI * 2;
  object.position.x = Math.cos(angle) * 3;
  object.position.z = Math.sin(angle) * 3;
  object.position.y = Math.sin(i) * 0.5;

  scene.add(object);
  effectObjects.push(object);
}

camera.position.set(0, 2, 5);
camera.lookAt(0, 0, 0);

// Animate with various effects
let effectTime = 0;

animate(() => {
  effectTime += 0.02;

  // Color grading effect - change scene mood
  const hue = (Math.sin(effectTime * 0.3) + 1) * 0.5 * 0.3; // Blue to purple range
  scene.background = new THREE.Color().setHSL(hue, 0.5, 0.1);

  // Glow effect on center sphere
  const glow = 0.6 + Math.sin(effectTime * 2) * 0.4;
  centerSphere.material.opacity = glow;
  centerSphere.scale.setScalar(1 + Math.sin(effectTime * 3) * 0.1);

  // Depth of field simulation
  effectObjects.forEach((obj, index) => {
    obj.rotation.y += 0.02;

    // Distance from camera affects opacity (simulating blur)
    const distance = obj.position.distanceTo(camera.position);
    obj.material.opacity = Math.max(0.4, 1 - (distance - 3) * 0.1);
    obj.material.transparent = true;

    // Orbit animation
    const orbitAngle = effectTime * 0.5 + (index / 6) * Math.PI * 2;
    obj.position.x = Math.cos(orbitAngle) * 3;
    obj.position.z = Math.sin(orbitAngle) * 3;
  });
});

console.log('🎨 Post-processing effects simulation:');
console.log('• Color grading (background mood)');
console.log('• Glow effects (pulsing center sphere)');
console.log('• Depth of field (distance-based transparency)');
console.log('🎬 Real post-processing uses render passes for better quality');

// Effect types explanation
console.log('✨ Common post-processing effects:');
console.log('• Bloom: Makes bright objects glow');
console.log('• Depth of Field: Blurs objects out of focus');
console.log('• Color Grading: Adjusts mood and atmosphere');
console.log('• Vignette: Darkens edges for focus');
console.log('• Film Grain: Adds texture and vintage feel');`,
    tasks: [
      {
        title: "🎭 Mood Master",
        description: "Create three different moods for the same scene using only post-processing effects - happy, mysterious, and dramatic",
        hint: "Use color grading, lighting changes, and atmospheric effects to transform the scene's emotional impact"
      },
      {
        title: "✨ Glow Gallery",
        description: "Create a gallery of objects with different types of glow effects - soft glow, harsh neon, magical sparkle",
        hint: "Layer transparent materials and use different colors and intensities to create various glow styles"
      },
      {
        title: "📷 Focus Director",
        description: "Create a scene where the focus point moves between different objects, blurring everything else",
        hint: "Use distance calculations to blur objects far from the current focus target"
      }
    ],
    examples: [
      {
        title: "Effect Performance Tips",
        description: "Balancing visual quality with performance",
        code: `// Performance optimization for effects:
// 1. Use lower resolution for expensive effects
// 2. Limit number of effect passes
// 3. Use simpler effects on mobile devices
// 4. Cache effect results when possible
// 5. Disable effects based on performance budget

const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
const effectQuality = isMobile ? 'low' : 'high';`
      }
    ]
  },

  10: {
    id: 10,
    title: "Shaders & Performance",
    difficulty: "Advanced",
    category: "Advanced",
    description: "Master the art of optimization! Learn how to make your 3D scenes run smoothly even with hundreds of objects, and understand how shaders work to create custom visual effects.",
    objectives: [
      "Understand how shaders control object appearance",
      "Learn optimization techniques for large scenes",
      "Create efficient rendering with many objects",
      "Balance visual quality with performance"
    ],
    theory: [
      "Shaders are small programs that run on the graphics card to control how objects look. They're incredibly fast because they process many pixels and vertices simultaneously.",
      "Performance optimization is about doing more with less - rendering many objects efficiently, reducing unnecessary calculations, and using the GPU's strengths.",
      "Level of Detail (LOD) systems show simple models when objects are far away and detailed models when they're close, saving processing power.",
      "Instancing allows you to render thousands of similar objects (like grass, trees, or particles) with minimal performance cost."
    ],
    concepts: [
      { name: "Shaders", description: "GPU programs that control object appearance", example: "Custom materials, special effects, animated surfaces" },
      { name: "Level of Detail (LOD)", description: "Different detail levels based on distance", example: "Detailed model up close, simple model far away" },
      { name: "Instancing", description: "Rendering many similar objects efficiently", example: "Thousands of grass blades or particles" },
      { name: "Culling", description: "Not rendering objects that can't be seen", example: "Objects behind camera or behind other objects" }
    ],
    learningPath: [
      {
        id: "step1-custom-materials",
        title: "Step 1: Custom Materials with Shaders",
        type: "concept",
        content: "Shaders let you create custom materials that can't be achieved with standard materials. They're like recipes that tell the graphics card exactly how to color each pixel of your objects.",
        explanation: "Think of shaders like custom paint formulas - instead of using pre-mixed colors, you can create your own unique effects by mixing colors mathematically in real-time.",
        codeExample: `// Create custom shader materials
const customObjects = [];

// Custom vertex shader (positions vertices)
const vertexShader = \`
  varying vec2 vUv;
  varying vec3 vPosition;

  void main() {
    vUv = uv;
    vPosition = position;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  }
\`;

// Custom fragment shader (colors pixels)
const fragmentShader = \`
  uniform float time;
  uniform vec3 color;
  varying vec2 vUv;
  varying vec3 vPosition;

  void main() {
    // Create animated wave pattern
    float wave = sin(vPosition.x * 10.0 + time * 2.0) * 0.5 + 0.5;

    // Mix colors based on UV coordinates and wave
    vec3 finalColor = color * (wave * 0.5 + 0.5);
    finalColor += vec3(vUv.x, vUv.y, 1.0 - vUv.x) * 0.3;

    gl_FragColor = vec4(finalColor, 1.0);
  }
\`;

// Create objects with custom shader materials
for (let i = 0; i < 3; i++) {
  const shaderMaterial = new THREE.ShaderMaterial({
    uniforms: {
      time: { value: 0 },
      color: { value: new THREE.Color().setHSL(i / 3, 0.8, 0.6) }
    },
    vertexShader: vertexShader,
    fragmentShader: fragmentShader
  });

  const geometry = new THREE.SphereGeometry(0.8, 32, 16);
  const mesh = new THREE.Mesh(geometry, shaderMaterial);
  mesh.position.x = (i - 1) * 3;

  scene.add(mesh);
  customObjects.push(mesh);
}

camera.position.set(0, 2, 6);
camera.lookAt(0, 0, 0);

// Animate shader uniforms
animate(() => {
  const time = Date.now() * 0.001;

  customObjects.forEach((obj, index) => {
    // Update shader time uniform
    obj.material.uniforms.time.value = time;

    // Rotate objects
    obj.rotation.y += 0.01 * (index + 1);
  });
});

console.log('🎨 Custom shader materials with animated effects');
console.log('Shaders run on GPU for maximum performance');
console.log('Each object has unique animated wave patterns');

// Shader explanation
console.log('⚡ Shader advantages:');
console.log('• Custom visual effects impossible with standard materials');
console.log('• GPU acceleration for complex calculations');
console.log('• Real-time animation and interaction');
console.log('• Efficient rendering of complex patterns');`,
        nextStep: "Next: Optimize scenes with many objects"
      },
      {
        id: "step2-instancing",
        title: "Step 2: Instancing for Many Objects",
        type: "concept",
        content: "When you need to render hundreds or thousands of similar objects (like grass, trees, or particles), instancing lets you do it efficiently by reusing the same geometry with different positions and properties.",
        explanation: "Think of instancing like using a cookie cutter - you have one shape (the geometry) but you can stamp it out many times in different places with different decorations (positions, colors, scales).",
        codeExample: `// Create an instanced mesh for many objects
const instanceCount = 100;
const geometry = new THREE.BoxGeometry(0.2, 0.2, 0.2);
const material = new THREE.MeshBasicMaterial({ color: 0x4ecdc4 });

// Create instanced mesh
const instancedMesh = new THREE.InstancedMesh(geometry, material, instanceCount);

// Set up each instance with different properties
const matrix = new THREE.Matrix4();
const color = new THREE.Color();

for (let i = 0; i < instanceCount; i++) {
  // Random position
  const x = (Math.random() - 0.5) * 10;
  const y = (Math.random() - 0.5) * 6;
  const z = (Math.random() - 0.5) * 10;

  // Random rotation
  const rotationX = Math.random() * Math.PI;
  const rotationY = Math.random() * Math.PI;
  const rotationZ = Math.random() * Math.PI;

  // Random scale
  const scale = 0.5 + Math.random() * 1.5;

  // Create transformation matrix
  matrix.makeRotationFromEuler(new THREE.Euler(rotationX, rotationY, rotationZ));
  matrix.setPosition(x, y, z);
  matrix.scale(new THREE.Vector3(scale, scale, scale));

  // Apply to instance
  instancedMesh.setMatrixAt(i, matrix);

  // Set random color for each instance
  color.setHSL(Math.random(), 0.8, 0.6);
  instancedMesh.setColorAt(i, color);
}

scene.add(instancedMesh);
camera.position.set(0, 3, 8);
camera.lookAt(0, 0, 0);

// Animate instances
animate(() => {
  const time = Date.now() * 0.001;

  for (let i = 0; i < instanceCount; i++) {
    // Get current matrix
    instancedMesh.getMatrixAt(i, matrix);

    // Extract position
    const position = new THREE.Vector3();
    position.setFromMatrixPosition(matrix);

    // Add floating motion
    position.y += Math.sin(time * 2 + i * 0.1) * 0.02;

    // Update rotation
    const rotation = new THREE.Euler(
      time + i * 0.1,
      time * 0.5 + i * 0.05,
      time * 0.3 + i * 0.07
    );

    // Recreate matrix with new transform
    matrix.makeRotationFromEuler(rotation);
    matrix.setPosition(position);

    instancedMesh.setMatrixAt(i, matrix);
  }

  // Tell Three.js to update the instances
  instancedMesh.instanceMatrix.needsUpdate = true;
});

console.log(\`🚀 Rendering \${instanceCount} objects efficiently with instancing\`);
console.log('Each object has unique position, rotation, scale, and color');
console.log('All rendered in a single draw call for maximum performance');

// Performance comparison
console.log('📊 Performance comparison:');
console.log(\`• Without instancing: \${instanceCount} draw calls\`);
console.log('• With instancing: 1 draw call');
console.log('• Result: 100x better performance!');`,
        nextStep: "Next: Implement Level of Detail (LOD)"
      },
      {
        id: "step3-level-of-detail",
        title: "Step 3: Level of Detail (LOD)",
        type: "concept",
        content: "**Level of Detail (LOD)** systems automatically switch between different quality versions based on distance from camera! 🔍 This mimics natural human vision for massive performance gains.\n\n👁️ **NATURAL VISION PRINCIPLE:**\n🔍 **Close objects** - see fine details, individual features\n🌄 **Distant objects** - appear as simple shapes, basic colors\n🧠 **Brain fills in** expected details automatically\n🎯 **LOD mimics** this natural behavior to save processing power\n💻 `new THREE.LOD()` // creates LOD system\n\n📏 **DISTANCE-BASED SWITCHING:**\n📐 **High detail** (0-50 units) - full geometry, all textures\n📊 **Medium detail** (50-200 units) - reduced polygons, smaller textures\n📉 **Low detail** (200+ units) - simple shapes, basic materials\n🔄 **Automatic switching** - based on camera distance\n💻 `lod.addLevel(highDetailMesh, 0)` // close distance\n💻 `lod.addLevel(lowDetailMesh, 200)` // far distance\n\n🎯 **PERFORMANCE BENEFITS:**\n⚡ **Massive polygon reduction** - 10,000 triangles → 100 triangles\n🖼️ **Texture memory savings** - 2048px → 256px textures\n📱 **Mobile optimization** - essential for smooth mobile performance\n🎮 **Stable frame rates** - maintains 60fps with many objects\n💻 `// 100x performance improvement possible`\n\n🏗️ **IMPLEMENTATION STRATEGIES:**\n🎨 **Artist-created** - manually model different detail levels\n🤖 **Automatic reduction** - algorithms simplify high-detail models\n📊 **Hybrid approach** - combine manual and automatic methods\n🎯 **Quality control** - ensure visual consistency across levels\n💻 `simplifyModifier.ratio = 0.1` // reduce to 10% polygons\n\n🌍 **REAL-WORLD EXAMPLES:**\n🌳 **Forest scenes** - detailed trees close, simple sprites distant\n🏙️ **City environments** - full buildings close, boxes distant\n👥 **Crowd systems** - detailed characters close, simple shapes far\n🚗 **Racing games** - detailed cars close, low-poly distant\n💻 `// Essential for open-world games`\n\n🔧 **ADVANCED TECHNIQUES:**\n📐 **Geometric LOD** - different polygon counts\n🖼️ **Texture LOD** - different resolution textures\n🎨 **Shader LOD** - simpler shaders for distant objects\n⚡ **Animation LOD** - reduced animation quality\n💻 `material.map = distanceBasedTexture` // texture switching",
        explanation: "Think of how you see the world - you can see fine details on things close to you, but distant objects appear as simple shapes. LOD systems mimic this natural behavior to save processing power.",
        codeExample: `// Create LOD system with different detail levels
const lodObjects = [];

// Create objects at different distances
for (let i = 0; i < 5; i++) {
  const distance = i * 3 + 2; // 2, 5, 8, 11, 14 units away

  // Create LOD group
  const lod = new THREE.LOD();

  // High detail version (close up)
  const highDetailGeometry = new THREE.SphereGeometry(0.8, 32, 16);
  const highDetailMaterial = new THREE.MeshBasicMaterial({
    color: new THREE.Color().setHSL(i / 5, 0.8, 0.6),
    wireframe: false
  });
  const highDetail = new THREE.Mesh(highDetailGeometry, highDetailMaterial);
  lod.addLevel(highDetail, 0); // Use from distance 0

  // Medium detail version
  const mediumDetailGeometry = new THREE.SphereGeometry(0.8, 16, 8);
  const mediumDetailMaterial = new THREE.MeshBasicMaterial({
    color: new THREE.Color().setHSL(i / 5, 0.8, 0.6),
    wireframe: false
  });
  const mediumDetail = new THREE.Mesh(mediumDetailGeometry, mediumDetailMaterial);
  lod.addLevel(mediumDetail, 5); // Use from distance 5

  // Low detail version (far away)
  const lowDetailGeometry = new THREE.BoxGeometry(1, 1, 1);
  const lowDetailMaterial = new THREE.MeshBasicMaterial({
    color: new THREE.Color().setHSL(i / 5, 0.8, 0.6),
    wireframe: true
  });
  const lowDetail = new THREE.Mesh(lowDetailGeometry, lowDetailMaterial);
  lod.addLevel(lowDetail, 10); // Use from distance 10

  // Position the LOD object
  lod.position.z = -distance;
  lod.position.x = (i - 2) * 1.5;

  scene.add(lod);
  lodObjects.push(lod);
}

camera.position.set(0, 2, 3);
camera.lookAt(0, 0, -8);

// Animate camera to see LOD switching
let cameraTime = 0;

animate(() => {
  cameraTime += 0.01;

  // Move camera back and forth to see LOD switching
  camera.position.z = 3 + Math.sin(cameraTime) * 8;
  camera.lookAt(0, 0, -8);

  // Update LOD calculations
  lodObjects.forEach(lod => {
    lod.update(camera);

    // Rotate for visual interest
    lod.rotation.y += 0.02;
  });

  // Display current camera distance
  const distance = camera.position.z;
  if (Math.floor(cameraTime * 10) % 60 === 0) { // Log every few seconds
    console.log(\`Camera distance: \${distance.toFixed(1)}\`);
  }
});

console.log('🔍 Level of Detail (LOD) system demonstration');
console.log('Watch objects change detail as camera moves:');
console.log('• Close (0-5 units): High detail spheres (32 segments)');
console.log('• Medium (5-10 units): Medium detail spheres (16 segments)');
console.log('• Far (10+ units): Low detail wireframe cubes');

// LOD benefits
console.log('⚡ LOD system benefits:');
console.log('• Automatic performance optimization');
console.log('• Maintains visual quality where it matters');
console.log('• Scales well with scene complexity');
console.log('• Transparent to the user');`,
        nextStep: "Next: Combine all optimization techniques"
      },
      {
        id: "step4-performance-optimization",
        title: "Step 4: Complete Performance Strategy",
        type: "concept",
        content: "The best performance comes from combining multiple optimization techniques - instancing for many similar objects, LOD for distance-based detail, culling for invisible objects, and efficient materials.",
        explanation: "Think of performance optimization like organizing a large event - you need crowd control (culling), efficient catering (instancing), appropriate seating (LOD), and good logistics (efficient rendering) all working together.",
        codeExample: `// Complete performance optimization demonstration
const optimizedScene = {
  instancedObjects: [],
  lodObjects: [],
  staticObjects: []
};

// 1. Instanced objects for many similar items (like grass or particles)
const particleCount = 200;
const particleGeometry = new THREE.SphereGeometry(0.05, 8, 8);
const particleMaterial = new THREE.MeshBasicMaterial({ color: 0x4ecdc4 });
const instancedParticles = new THREE.InstancedMesh(particleGeometry, particleMaterial, particleCount);

const matrix = new THREE.Matrix4();
const color = new THREE.Color();

for (let i = 0; i < particleCount; i++) {
  // Random position in a large area
  const x = (Math.random() - 0.5) * 20;
  const y = Math.random() * 10;
  const z = (Math.random() - 0.5) * 20;

  matrix.setPosition(x, y, z);
  instancedParticles.setMatrixAt(i, matrix);

  color.setHSL(Math.random(), 0.8, 0.6);
  instancedParticles.setColorAt(i, color);
}

scene.add(instancedParticles);
optimizedScene.instancedObjects.push(instancedParticles);

// 2. LOD objects for important scene elements
for (let i = 0; i < 8; i++) {
  const lod = new THREE.LOD();

  // High detail
  const highDetail = new THREE.Mesh(
    new THREE.CylinderGeometry(0.3, 0.5, 2, 16),
    new THREE.MeshBasicMaterial({ color: 0xff6b6b })
  );
  lod.addLevel(highDetail, 0);

  // Low detail
  const lowDetail = new THREE.Mesh(
    new THREE.BoxGeometry(0.6, 2, 0.6),
    new THREE.MeshBasicMaterial({ color: 0xff6b6b })
  );
  lod.addLevel(lowDetail, 8);

  const angle = (i / 8) * Math.PI * 2;
  lod.position.set(
    Math.cos(angle) * 6,
    1,
    Math.sin(angle) * 6
  );

  scene.add(lod);
  optimizedScene.lodObjects.push(lod);
}

// 3. Static objects (optimized geometry)
const staticGeometry = new THREE.PlaneGeometry(30, 30);
const staticMaterial = new THREE.MeshBasicMaterial({
  color: 0x228B22,
  side: THREE.DoubleSide
});
const ground = new THREE.Mesh(staticGeometry, staticMaterial);
ground.rotation.x = -Math.PI / 2;
ground.position.y = -1;
scene.add(ground);

camera.position.set(0, 5, 10);
camera.lookAt(0, 0, 0);

// Performance monitoring
let frameCount = 0;
let lastTime = Date.now();

// Optimized animation loop
animate(() => {
  const currentTime = Date.now();
  const deltaTime = currentTime - lastTime;

  // Update instanced particles
  for (let i = 0; i < particleCount; i++) {
    instancedParticles.getMatrixAt(i, matrix);
    const position = new THREE.Vector3();
    position.setFromMatrixPosition(matrix);

    // Simple floating animation
    position.y += Math.sin(currentTime * 0.001 + i * 0.1) * 0.01;

    matrix.setPosition(position);
    instancedParticles.setMatrixAt(i, matrix);
  }
  instancedParticles.instanceMatrix.needsUpdate = true;

  // Update LOD objects
  optimizedScene.lodObjects.forEach(lod => {
    lod.update(camera);
    lod.rotation.y += 0.01;
  });

  // Performance monitoring
  frameCount++;
  if (frameCount % 60 === 0) { // Every 60 frames
    const fps = 1000 / (deltaTime || 1);
    console.log(\`FPS: \${fps.toFixed(1)}, Objects: \${particleCount + optimizedScene.lodObjects.length}\`);
  }

  lastTime = currentTime;
});

console.log('🚀 Complete performance optimization strategy:');
console.log(\`• \${particleCount} instanced particles (1 draw call)\`);
console.log(\`• \${optimizedScene.lodObjects.length} LOD objects (automatic detail switching)\`);
console.log('• Efficient materials and geometry');
console.log('• Performance monitoring');

// Optimization checklist
console.log('✅ Performance optimization checklist:');
console.log('• Use instancing for many similar objects');
console.log('• Implement LOD for distance-based detail');
console.log('• Minimize draw calls and state changes');
console.log('• Use efficient materials (avoid transparency when possible)');
console.log('• Cull objects outside camera view');
console.log('• Monitor performance and adjust accordingly');`,
        nextStep: "🎉 You're now a 3D performance optimization expert!"
      }
    ],
    initialCode: `// ⚡ Performance & Optimization Demo

// Create an efficient scene with many objects
const performanceObjects = [];

// 1. Instanced mesh for many similar objects
const instanceCount = 50;
const geometry = new THREE.BoxGeometry(0.3, 0.3, 0.3);
const material = new THREE.MeshBasicMaterial({ color: 0x4ecdc4 });
const instancedMesh = new THREE.InstancedMesh(geometry, material, instanceCount);

// Set up instances
const matrix = new THREE.Matrix4();
const color = new THREE.Color();

for (let i = 0; i < instanceCount; i++) {
  // Random position
  const x = (Math.random() - 0.5) * 10;
  const y = Math.random() * 5;
  const z = (Math.random() - 0.5) * 10;

  // Random scale
  const scale = 0.5 + Math.random() * 1;

  matrix.makeScale(scale, scale, scale);
  matrix.setPosition(x, y, z);
  instancedMesh.setMatrixAt(i, matrix);

  // Random color
  color.setHSL(Math.random(), 0.8, 0.6);
  instancedMesh.setColorAt(i, color);
}

scene.add(instancedMesh);

// 2. LOD objects for important elements
for (let i = 0; i < 5; i++) {
  const lod = new THREE.LOD();

  // High detail version
  const highDetail = new THREE.Mesh(
    new THREE.SphereGeometry(0.8, 32, 16),
    new THREE.MeshBasicMaterial({ color: 0xff6b6b })
  );
  lod.addLevel(highDetail, 0);

  // Low detail version
  const lowDetail = new THREE.Mesh(
    new THREE.BoxGeometry(1, 1, 1),
    new THREE.MeshBasicMaterial({ color: 0xff6b6b, wireframe: true })
  );
  lod.addLevel(lowDetail, 5);

  lod.position.set((i - 2) * 3, 1, -5);
  scene.add(lod);
  performanceObjects.push(lod);
}

camera.position.set(0, 3, 8);
camera.lookAt(0, 0, 0);

// Performance monitoring
let frameCount = 0;
let lastTime = Date.now();

// Optimized animation
animate(() => {
  const time = Date.now() * 0.001;

  // Animate instanced objects efficiently
  for (let i = 0; i < instanceCount; i++) {
    instancedMesh.getMatrixAt(i, matrix);
    const position = new THREE.Vector3();
    position.setFromMatrixPosition(matrix);

    // Add floating motion
    position.y += Math.sin(time * 2 + i * 0.1) * 0.02;

    matrix.setPosition(position);
    instancedMesh.setMatrixAt(i, matrix);
  }
  instancedMesh.instanceMatrix.needsUpdate = true;

  // Update LOD objects
  performanceObjects.forEach(lod => {
    lod.update(camera);
    lod.rotation.y += 0.01;
  });

  // Performance monitoring
  frameCount++;
  if (frameCount % 120 === 0) { // Every 2 seconds
    const currentTime = Date.now();
    const fps = 1000 / ((currentTime - lastTime) / 120);
    console.log(\`Performance: \${fps.toFixed(1)} FPS with \${instanceCount + performanceObjects.length} objects\`);
    lastTime = currentTime;
  }
});

console.log('⚡ Performance optimization techniques:');
console.log(\`• \${instanceCount} instanced objects (1 draw call)\`);
console.log(\`• \${performanceObjects.length} LOD objects (automatic detail switching)\`);
console.log('• Efficient animation and rendering');

console.log('🚀 Optimization strategies:');
console.log('• Instancing: Many similar objects efficiently');
console.log('• LOD: Detail based on distance');
console.log('• Culling: Hide objects outside view');
console.log('• Efficient materials: Minimize state changes');`,
    tasks: [
      {
        title: "🌾 Grass Field",
        description: "Create a field with thousands of grass blades using instancing, with wind animation",
        hint: "Use InstancedMesh with thin cylinder geometry, animate with sine waves for wind effect"
      },
      {
        title: "🏙️ City Builder",
        description: "Create a city with buildings that use LOD - detailed when close, simple when far",
        hint: "Use LOD objects with high-detail and low-detail building models based on camera distance"
      },
      {
        title: "🎮 Performance Monitor",
        description: "Build a performance monitoring system that shows FPS, object count, and draw calls",
        hint: "Track frame times, count visible objects, and display performance metrics on screen"
      }
    ],
    examples: [
      {
        title: "Instancing Pattern",
        description: "Standard setup for rendering many similar objects",
        code: `const instancedMesh = new THREE.InstancedMesh(geometry, material, count);
const matrix = new THREE.Matrix4();

for (let i = 0; i < count; i++) {
  matrix.setPosition(x, y, z);
  instancedMesh.setMatrixAt(i, matrix);
}

// In animation loop:
instancedMesh.instanceMatrix.needsUpdate = true;`
      }
    ]
  },

  11: {
    id: 11,
    title: "Creating a Galaxy",
    difficulty: "Intermediate",
    category: "Creative Projects",
    description: "Create the same mesmerizing particle galaxy featured on our home page! Learn to build swirling particle systems with beautiful blue-white colors and smooth animations.",
    objectives: [
      "Recreate the beautiful home page galaxy effect",
      "Master particle systems with BufferGeometry",
      "Apply realistic star colors and transparency",
      "Implement smooth rotation animations"
    ],
    theory: [
      "Particle systems efficiently render thousands of small objects like stars and cosmic dust using minimal performance overhead.",
      "Spiral galaxies follow mathematical patterns that can be recreated using trigonometric functions and noise algorithms.",
      "Cosmic visual effects rely on careful color selection, transparency, and additive blending to create realistic space environments.",
      "Camera animation through 3D space requires smooth interpolation and careful timing to create cinematic experiences."
    ],
    concepts: [
      {
        name: "Particle Systems",
        description: "Efficient rendering of thousands of small objects",
        example: "Stars, cosmic dust, nebulae using BufferGeometry"
      },
      {
        name: "Procedural Generation",
        description: "Mathematical creation of galaxy structures",
        example: "Spiral arms using trigonometry and random distribution"
      },
      {
        name: "Cosmic Visual Effects",
        description: "Realistic space rendering techniques",
        example: "Glowing stars, nebula colors, depth-based opacity"
      },
      {
        name: "Camera Animation",
        description: "Smooth movement through 3D space",
        example: "Orbital motion, zoom effects, cinematic transitions"
      }
    ],
    learningPath: [
      {
        id: "step1-star-field",
        title: "Step 1: Recreate the Home Page Galaxy",
        type: "implementation",
        content: "Let's recreate the exact same beautiful particle galaxy you see on our home page! 🌌\n\n✨ **HOME PAGE GALAXY FEATURES:**\n🌟 **1000 particles** - perfect balance of beauty and performance\n💙 **Blue-white colors** - realistic star colors like real galaxies\n🌀 **Disk formation** - particles arranged in a flat galaxy disk\n🎭 **Transparency** - creates depth and realistic glow\n💻 `size: 0.05` // tiny particles for realistic star appearance\n\n🎨 **BEAUTIFUL COLOR SYSTEM:**\n🔴 **Red channel** - 0.5 to 1.0 (warm base)\n🟢 **Green channel** - 0.3 to 1.0 (variable intensity)\n🔵 **Blue channel** - always 1.0 (full blue for star-like appearance)\n🌟 **Result** - gorgeous blue-white stars like real space\n💻 `colors[i3 + 2] = 1` // full blue creates star-like color\n\n🌀 **GALAXY DISK FORMATION:**\n📐 **Polar coordinates** - radius and angle for natural distribution\n📏 **Thin disk** - y-position limited to create flat galaxy\n🎯 **Radius variation** - stars spread from center to edge\n⭕ **Circular pattern** - Math.cos/sin creates perfect circle\n💻 `positions[i3 + 1] = (Math.random() - 0.5) * 2` // thin disk\n\n⚡ **PERFORMANCE OPTIMIZATION:**\n📦 **BufferGeometry** - efficient storage for 1000 particles\n🎨 **PointsMaterial** - GPU-optimized particle rendering\n🔄 **Single draw call** - all particles rendered together\n💫 **Smooth animation** - gentle rotation at 0.002 radians/frame\n💻 `particleSystem.rotation.y += 0.002` // smooth rotation",
        explanation: "This galaxy uses the exact same code as our home page showcase! The secret is in the color system (blue-white like real stars), the disk formation (flat like real galaxies), and the tiny particle size (0.05) that creates a realistic starfield effect.",
        codeExample: `// Create beautiful particle galaxy like the home page
const particleCount = 1000;
const particles = new THREE.BufferGeometry();
const positions = new Float32Array(particleCount * 3);
const colors = new Float32Array(particleCount * 3);

for (let i = 0; i < particleCount; i++) {
  const i3 = i * 3;
  const radius = Math.random() * 5;
  const angle = Math.random() * Math.PI * 2;

  // Position in galaxy disk formation
  positions[i3] = Math.cos(angle) * radius;
  positions[i3 + 1] = (Math.random() - 0.5) * 2;
  positions[i3 + 2] = Math.sin(angle) * radius;

  // Beautiful blue-white star colors
  colors[i3] = 0.5 + Math.random() * 0.5;
  colors[i3 + 1] = 0.3 + Math.random() * 0.7;
  colors[i3 + 2] = 1;
}

particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));

const material = new THREE.PointsMaterial({
  size: 0.05,
  vertexColors: true,
  transparent: true,
  opacity: 0.8
});

const particleSystem = new THREE.Points(particles, material);
scene.add(particleSystem);`,
        nextStep: "Next: Generate spiral galaxy arms"
      },
      {
        id: "step2-spiral-arms",
        title: "Step 2: Generate Spiral Galaxy Arms",
        type: "implementation",
        content: "Now let's create the iconic spiral arms that make our galaxy look realistic! 🌌\n\n🌀 **SPIRAL MATHEMATICS:**\n📐 **Logarithmic spirals** - the natural pattern galaxies follow\n🔢 **Polar coordinates** - radius and angle define spiral shape\n🎯 **Multiple arms** - typically 2-4 arms for realistic appearance\n💻 `r = a * Math.exp(b * theta)` // logarithmic spiral equation\n\n⭐ **STAR DISTRIBUTION:**\n🎲 **Density variation** - more stars in spiral arms, fewer between\n📏 **Distance from center** - star density decreases with radius\n🌟 **Arm thickness** - gradual falloff from arm center\n🔄 **Rotation** - slight rotation gives dynamic appearance\n💻 `Math.sin(theta * armCount)` // creates multiple arms\n\n🎨 **VISUAL REALISM:**\n🌈 **Color gradients** - blue stars in arms, yellow/red in center\n✨ **Brightness variation** - brighter stars in spiral arms\n🌫️ **Cosmic dust** - darker regions between arms\n💫 **Subtle animation** - slow rotation brings galaxy to life\n💻 `color.setHSL(hue, saturation, brightness)` // realistic star colors",
        explanation: "Spiral galaxies like our Milky Way follow mathematical patterns that we can recreate in code. The spiral arms aren't solid structures - they're density waves where stars cluster together, creating the beautiful spiral pattern we see in space.",
        codeExample: `// Generate spiral galaxy arms
const armCount = 4;
const galaxyRadius = 50;
const armStars = 3000;

for (let i = 0; i < armStars; i++) {
  // Calculate spiral position
  const t = i / armStars;
  const radius = t * galaxyRadius;
  const angle = t * Math.PI * 6; // 3 full rotations

  // Create multiple spiral arms
  for (let arm = 0; arm < armCount; arm++) {
    const armAngle = angle + (arm * Math.PI * 2) / armCount;

    // Add some randomness for natural look
    const x = Math.cos(armAngle) * radius + (Math.random() - 0.5) * 5;
    const z = Math.sin(armAngle) * radius + (Math.random() - 0.5) * 5;
    const y = (Math.random() - 0.5) * 2; // Thin galaxy disk

    // Create star at this position
    const star = new THREE.Mesh(
      new THREE.SphereGeometry(0.1, 8, 8),
      new THREE.MeshBasicMaterial({
        color: new THREE.Color().setHSL(0.6 - t * 0.3, 0.8, 0.9)
      })
    );

    star.position.set(x, y, z);
    scene.add(star);
  }
}`,
        nextStep: "Next: Add cosmic colors and effects"
      },
      {
        id: "step3-cosmic-effects",
        title: "Step 3: Cosmic Colors and Effects",
        type: "enhancement",
        content: "Let's add the stunning visual effects that make our galaxy truly spectacular! 🎆\n\n🌈 **COSMIC COLOR PALETTE:**\n💙 **Blue giants** - hot, young stars in spiral arms\n💛 **Yellow stars** - sun-like stars in middle regions\n❤️ **Red giants** - older stars in galaxy center\n💜 **Nebula colors** - pink and purple gas clouds\n💻 `color.setHSL(hue, saturation, luminosity)` // HSL for natural colors\n\n✨ **GLOWING EFFECTS:**\n🌟 **Additive blending** - stars glow and blend naturally\n💫 **Bloom effect** - bright stars create halos\n🌫️ **Atmospheric perspective** - distant objects appear dimmer\n🎭 **Depth-based opacity** - creates realistic depth\n💻 `material.blending = THREE.AdditiveBlending` // realistic glow\n\n🌌 **NEBULA AND DUST:**\n☁️ **Gas clouds** - colorful regions of star formation\n🌫️ **Dark matter** - mysterious dark regions\n✨ **Emission nebulae** - glowing hydrogen clouds\n🎨 **Reflection nebulae** - blue scattered light\n💻 `new THREE.SpriteMaterial()` // for cloud-like effects\n\n🎬 **DYNAMIC LIGHTING:**\n💡 **Central bulge** - bright galactic core\n🌟 **Point lights** - brightest stars illuminate nearby objects\n🌅 **Ambient glow** - subtle overall illumination\n⚡ **Performance balance** - beautiful but efficient\n💻 `new THREE.PointLight(color, intensity, distance)` // star lighting",
        explanation: "The beauty of a galaxy comes from its colors and lighting effects. Different types of stars emit different colors based on their temperature, and the interplay of light and cosmic dust creates the breathtaking visuals we see in space photography.",
        codeExample: `// Add cosmic colors and glowing effects
const galaxyCenter = new THREE.Mesh(
  new THREE.SphereGeometry(2, 32, 32),
  new THREE.MeshBasicMaterial({
    color: 0xffaa00,
    transparent: true,
    opacity: 0.8
  })
);
scene.add(galaxyCenter);

// Add central glow
const centralGlow = new THREE.PointLight(0xffaa00, 2, 100);
centralGlow.position.set(0, 0, 0);
scene.add(centralGlow);

// Create nebula clouds
for (let i = 0; i < 20; i++) {
  const cloud = new THREE.Sprite(
    new THREE.SpriteMaterial({
      color: new THREE.Color().setHSL(
        Math.random() * 0.3 + 0.7, // Purple to pink
        0.8,
        0.6
      ),
      transparent: true,
      opacity: 0.3,
      blending: THREE.AdditiveBlending
    })
  );

  cloud.position.set(
    (Math.random() - 0.5) * 80,
    (Math.random() - 0.5) * 10,
    (Math.random() - 0.5) * 80
  );
  cloud.scale.setScalar(Math.random() * 10 + 5);
  scene.add(cloud);
}

// Ambient space lighting
const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
scene.add(ambientLight);`,
        nextStep: "Next: Animate camera movement"
      },
      {
        id: "step4-camera-animation",
        title: "Step 4: Cinematic Camera Movement",
        type: "animation",
        content: "Let's create smooth, cinematic camera movement that takes viewers on a journey through our galaxy! 🎬\n\n🎥 **CAMERA CHOREOGRAPHY:**\n🌀 **Orbital motion** - circle around the galaxy to show its structure\n🔍 **Zoom effects** - fly closer to see details, pull back for overview\n📐 **Banking turns** - tilt camera during turns for cinematic feel\n🎯 **Focus points** - look at interesting features during movement\n💻 `camera.position.set(x, y, z)` // smooth position changes\n\n⏰ **SMOOTH INTERPOLATION:**\n🔄 **Linear interpolation** - smooth transitions between positions\n📈 **Easing functions** - natural acceleration and deceleration\n🎭 **Keyframe animation** - define important camera positions\n⚡ **Performance** - 60fps smooth movement\n💻 `THREE.MathUtils.lerp(start, end, alpha)` // smooth interpolation\n\n🌌 **SPACE NAVIGATION:**\n🚀 **Approach sequence** - start far away, fly into galaxy\n🌟 **Spiral tour** - follow the spiral arms inward\n🏠 **Return journey** - pull back to show full galaxy\n🔄 **Loop animation** - seamless continuous movement\n💻 `Math.sin(time)` // creates smooth oscillating motion\n\n🎮 **INTERACTIVE CONTROLS:**\n🖱️ **Mouse control** - let users explore manually\n⌨️ **Keyboard shortcuts** - quick camera presets\n📱 **Touch support** - mobile-friendly navigation\n🎚️ **Speed control** - adjust animation speed\n💻 `new THREE.OrbitControls(camera, renderer.domElement)` // user control",
        explanation: "Great camera movement is like directing a space documentary. You want to show the galaxy's grandeur while guiding viewers through an engaging visual journey that reveals both the big picture and fascinating details.",
        codeExample: `// Cinematic camera animation
let time = 0;
const cameraRadius = 80;
const cameraHeight = 20;

animate(() => {
  time += 0.01;

  // Orbital camera movement
  camera.position.x = Math.cos(time) * cameraRadius;
  camera.position.z = Math.sin(time) * cameraRadius;
  camera.position.y = Math.sin(time * 0.5) * cameraHeight;

  // Always look at galaxy center
  camera.lookAt(0, 0, 0);

  // Rotate the galaxy slowly
  if (stars) {
    stars.rotation.y += 0.001;
  }

  // Add some camera shake for realism
  camera.position.x += (Math.random() - 0.5) * 0.1;
  camera.position.y += (Math.random() - 0.5) * 0.1;
  camera.position.z += (Math.random() - 0.5) * 0.1;
});

// Smooth zoom effect
const zoomIn = () => {
  const targetRadius = 30;
  const currentRadius = Math.sqrt(
    camera.position.x ** 2 + camera.position.z ** 2
  );

  if (currentRadius > targetRadius) {
    const factor = 0.95;
    camera.position.multiplyScalar(factor);
  }
};

// Call zoomIn() to gradually zoom into the galaxy
console.log('🎬 Cinematic galaxy tour in progress!');`,
        nextStep: "🎉 You've created a stunning 3D galaxy!"
      }
    ],
    initialCode: `// 🌌 Beautiful Particle Galaxy - Just Like the Home Page!

// This creates the same mesmerizing galaxy you see on the home page
// with swirling particles and dynamic colors

console.log('🌌 Creating beautiful particle galaxy...');

// Set dark space background
scene.background = new THREE.Color(0x0a0a0a);

// Create particle system with 1000 particles
const particleCount = 1000;
const particles = new THREE.BufferGeometry();
const positions = new Float32Array(particleCount * 3);
const colors = new Float32Array(particleCount * 3);

// Generate particles in a spiral galaxy pattern
for (let i = 0; i < particleCount; i++) {
  const i3 = i * 3;
  const radius = Math.random() * 5;
  const angle = Math.random() * Math.PI * 2;

  // Position particles in a disk formation
  positions[i3] = Math.cos(angle) * radius;         // x
  positions[i3 + 1] = (Math.random() - 0.5) * 2;   // y (thin disk)
  positions[i3 + 2] = Math.sin(angle) * radius;    // z

  // Beautiful blue-white colors like real stars
  colors[i3] = 0.5 + Math.random() * 0.5;     // red
  colors[i3 + 1] = 0.3 + Math.random() * 0.7; // green
  colors[i3 + 2] = 1;                         // blue (always full)
}

// Set up the particle geometry
particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));

// Create beautiful glowing material
const material = new THREE.PointsMaterial({
  size: 0.05,
  vertexColors: true,
  transparent: true,
  opacity: 0.8
});

// Create the particle system
const particleSystem = new THREE.Points(particles, material);
scene.add(particleSystem);

// Position camera for optimal viewing
camera.position.z = 8;

// Animate with gentle rotation
animate(() => {
  particleSystem.rotation.y += 0.002;
});

console.log('✨ Beautiful particle galaxy created!');
console.log('🌟 1000 particles swirling in space');
console.log('💫 Same galaxy as the home page showcase');
console.log('🎯 Follow the learning steps to understand how it works');`,
    tasks: [
      { title: "Star Density", description: "Experiment with different star counts and see how it affects performance", hint: "Try 5,000, 20,000, and 50,000 stars" },
      { title: "Galaxy Shape", description: "Modify the spiral equation to create different galaxy types", hint: "Change the angle multiplier and arm count" },
      { title: "Color Schemes", description: "Create different cosmic color palettes", hint: "Adjust HSL values for different stellar populations" },
      { title: "Camera Paths", description: "Design different camera movements through the galaxy", hint: "Try figure-8 patterns, zoom sequences, or spiral approaches" }
    ],
    examples: [
      { title: "Particle Systems", description: "Efficient rendering of thousands of objects", code: "new THREE.Points(geometry, material)" },
      { title: "Spiral Mathematics", description: "Creating realistic galaxy arms", code: "angle = t * Math.PI * rotations + armOffset" },
      { title: "Cosmic Colors", description: "Realistic star and nebula colors", code: "color.setHSL(hue, saturation, lightness)" }
    ]
  },

  12: {
    id: 12,
    title: "AI-Generated Lessons",
    difficulty: "Advanced",
    category: "AI Integration",
    description: "Generate custom Three.js lessons using AI! Create personalized learning experiences by describing what you want to build, and watch as AI creates complete lessons with code examples, explanations, and step-by-step instructions.",
    objectives: [
      "Use AI to generate custom Three.js lessons",
      "Learn from AI-created code examples and explanations",
      "Explore creative 3D concepts through AI assistance",
      "Understand how AI can enhance learning experiences"
    ],
    theory: [
      "AI-powered lesson generation uses large language models to create educational content tailored to specific learning goals and skill levels.",
      "Natural language processing enables AI to understand user requests and generate appropriate Three.js code examples with detailed explanations.",
      "Machine learning models trained on vast amounts of code and documentation can provide accurate, up-to-date programming guidance.",
      "AI-assisted learning creates personalized educational experiences that adapt to individual interests and learning styles."
    ],
    concepts: [
      {
        name: "AI Lesson Generation",
        description: "Using AI to create custom educational content",
        example: "Prompt: 'Create a bouncing ball' → Complete lesson with code and explanations"
      },
      {
        name: "Natural Language to Code",
        description: "Converting human descriptions into working Three.js code",
        example: "Text descriptions become functional 3D scenes with proper explanations"
      },
      {
        name: "Personalized Learning",
        description: "AI adapts content to individual learning needs",
        example: "Beginner vs advanced explanations for the same concept"
      }
    ],
    learningPath: [
      {
        id: "ai-lesson-generator",
        title: "AI Lesson Generator",
        type: "interactive",
        content: "Use the AI lesson generator below to create custom Three.js lessons! Simply describe what you want to learn or build, and the AI will create a complete lesson with code examples, explanations, and step-by-step instructions.\n\n🤖 **HOW IT WORKS:**\n💬 **Describe your idea** - tell the AI what you want to create\n🧠 **AI processes** - generates lesson content using advanced language models\n📚 **Complete lesson** - receive code, explanations, and learning steps\n🎯 **Learn and experiment** - follow the lesson and modify the code\n\n✨ **EXAMPLE PROMPTS:**\n• 'Create a solar system with orbiting planets'\n• 'Build a particle system with floating stars'\n• 'Make a bouncing ball animation'\n• 'Design a procedural forest scene'\n• 'Create a water simulation with waves'",
        explanation: "The AI lesson generator demonstrates how artificial intelligence can enhance learning by creating personalized educational content. This represents the future of adaptive learning systems.",
        codeExample: "// AI will generate custom code based on your prompt",
        nextStep: "Try the AI lesson generator below!"
      }
    ],
    initialCode: `// 🤖 AI Lesson Generator Demo
// This lesson demonstrates AI-powered learning!

console.log('🤖 Welcome to AI-Generated Lessons!');
console.log('✨ Use the lesson generator below to create custom Three.js lessons');

// Create a simple demo scene while you explore AI generation
const geometry = new THREE.BoxGeometry(2, 2, 2);
const material = new THREE.MeshStandardMaterial({
  color: 0x00ff88,
  metalness: 0.3,
  roughness: 0.4
});
const cube = createMesh(geometry, material);
scene.add(cube);

// Add lighting
const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
scene.add(ambientLight);

const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
directionalLight.position.set(5, 5, 5);
scene.add(directionalLight);

// Position camera
camera.position.set(4, 4, 6);
camera.lookAt(0, 0, 0);

// Animate the cube
animate(() => {
  cube.rotation.x += 0.01;
  cube.rotation.y += 0.01;

  // Color cycling effect
  const time = Date.now() * 0.001;
  cube.material.color.setHSL((time * 0.1) % 1, 0.7, 0.6);
});

console.log('🎯 Try the AI lesson generator below!');
console.log('💡 Example prompts:');
console.log('• "Create a solar system with orbiting planets"');
console.log('• "Build a particle system with floating stars"');
console.log('• "Make a bouncing ball animation"');`,
    tasks: [
      { title: "AI Prompt Writing", description: "Experiment with different prompts to generate various types of lessons", hint: "Try specific vs general prompts, different complexity levels" },
      { title: "Code Analysis", description: "Study the AI-generated code to understand new techniques", hint: "Look for patterns, functions, and approaches you haven't seen before" },
      { title: "Lesson Customization", description: "Modify AI-generated lessons to match your learning goals", hint: "Add your own features, change parameters, combine concepts" }
    ],
    examples: [
      { title: "AI Prompting", description: "Effective ways to request AI-generated lessons", code: "// Be specific: 'Create a rotating cube with rainbow colors'" },
      { title: "Learning from AI", description: "How to extract maximum value from AI-generated content", code: "// Study the code structure, comments, and explanations" },
      { title: "AI Enhancement", description: "Using AI to expand your Three.js knowledge", code: "// Ask for variations, optimizations, or advanced features" }
    ]
  }
};

export const getLessonData = (lessonId) => {
  return lessons[lessonId] || null;
};

export const getAllLessons = () => {
  return Object.values(lessons);
};
