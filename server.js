// Local development server for API endpoints
import express from 'express'
import cors from 'cors'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'
import { readFileSync } from 'fs'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const app = express()
const PORT = 3001

// Middleware
app.use(cors())
app.use(express.json())

// Import the Vercel API function
const generateLessonHandler = async (req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type')

  if (req.method === 'OPTIONS') {
    res.status(200).end()
    return
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { systemPrompt, userPrompt } = req.body

    if (!systemPrompt || !userPrompt) {
      return res.status(400).json({ error: 'Missing required prompts' })
    }

    // Check for OpenAI API key
    const apiKey = process.env.OPENAI_API_KEY
    if (!apiKey) {
      console.error('OpenAI API key not found in environment variables')
      return res.status(500).json({ error: 'OpenAI API key not configured' })
    }

    console.log('API Key found, length:', apiKey.length)
    console.log('Generating lesson for prompt:', userPrompt)

    // Call OpenAI API
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: userPrompt
          }
        ],
        max_tokens: 3000,
        temperature: 0.7,
      }),
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
      console.error('OpenAI API error:', response.status, response.statusText, errorData)
      
      // Handle specific error types
      if (response.status === 401) {
        return res.status(500).json({ error: 'Invalid OpenAI API key' })
      } else if (response.status === 429) {
        return res.status(500).json({ error: 'OpenAI API rate limit exceeded' })
      } else if (response.status === 402) {
        return res.status(500).json({ error: 'OpenAI API quota exceeded - check billing' })
      }
      
      return res.status(500).json({ 
        error: 'Failed to generate lesson content',
        details: errorData.error?.message || 'Unknown OpenAI error'
      })
    }

    const data = await response.json()
    const lessonContent = data.choices[0]?.message?.content

    if (!lessonContent) {
      return res.status(500).json({ error: 'No content generated' })
    }

    // Try to parse the JSON response
    let lessonData
    try {
      lessonData = JSON.parse(lessonContent)
    } catch (parseError) {
      console.error('Failed to parse AI response:', parseError)
      return res.status(500).json({ error: 'Invalid lesson format generated' })
    }

    return res.status(200).json({ lesson: lessonData })

  } catch (error) {
    console.error('API error:', error)
    return res.status(500).json({ 
      error: 'Internal server error',
      details: error.message
    })
  }
}

// API routes
app.post('/api/generate-lesson', generateLessonHandler)

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Local API server is running' })
})

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Local API server running on http://localhost:${PORT}`)
  console.log(`📡 API endpoint: http://localhost:${PORT}/api/generate-lesson`)
  console.log(`🔑 OpenAI API Key: ${process.env.OPENAI_API_KEY ? 'Found' : 'Missing'}`)
})
