.lesson-content {
  color: #ffffff;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 0;
  height: 100%;
  overflow-y: auto;
  position: relative;
  z-index: 1;
}

.lesson-content-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #9ca3af;
}

.content-section {
  margin-bottom: 0;
  border: 1px solid #333;
  border-radius: 0.5rem;
  overflow: hidden;
  background: #1a1a1a;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
  width: 100%;
  flex-shrink: 0; /* Prevent sections from shrinking */
}

.content-section:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.section-header {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 1.75rem;
  background: linear-gradient(135deg, #2a2a2a 0%, #252525 100%);
  border: none;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  position: relative;
}

.section-header:hover {
  background: linear-gradient(135deg, #3a3a3a 0%, #353535 100%);
  transform: translateY(-1px);
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #3b82f6, transparent);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.section-header:hover::after {
  opacity: 1;
}

.section-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #ffffff;
}

.section-content {
  padding: 1.5rem;
  background: #1a1a1a;
  border-top: 1px solid #333;
  line-height: 1.6;
}

.section-content p {
  margin: 0 0 1rem 0;
  line-height: 1.6;
  color: #e5e7eb;
}

.section-content p:last-child {
  margin-bottom: 0;
}

/* Objectives */
.objectives {
  margin-top: 1rem;
}

.objectives h4 {
  margin: 0 0 0.5rem 0;
  color: #3b82f6;
  font-size: 0.875rem;
  font-weight: 600;
}

.objectives ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #d1d5db;
}

.objectives li {
  margin-bottom: 0.5rem;
}

/* Concepts Grid */
.concepts-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.concept-card {
  background: #2a2a2a;
  padding: 1.25rem;
  border-radius: 0.5rem;
  border: 1px solid #374151;
  transition: all 0.2s ease;
}

.concept-card:hover {
  background: #333333;
  border-color: #3b82f6;
  transform: translateY(-1px);
}

.concept-card h4 {
  margin: 0 0 0.5rem 0;
  color: #3b82f6;
  font-size: 0.875rem;
  font-weight: 600;
}

.concept-card p {
  margin: 0 0 0.75rem 0;
  font-size: 0.875rem;
  color: #d1d5db;
}

.concept-example {
  margin-top: 0.75rem;
  font-size: 0.75rem;
}

.concept-example strong {
  color: #f59e0b;
}

.concept-example code {
  display: block;
  background: #1a1a1a;
  padding: 0.5rem;
  border-radius: 0.25rem;
  margin-top: 0.25rem;
  font-family: 'Courier New', monospace;
  color: #10b981;
  border: 1px solid #333;
}

/* Tasks */
.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.task-item {
  display: flex;
  gap: 0.75rem;
  padding: 1rem;
  background: #2a2a2a;
  border-radius: 0.375rem;
  border: 1px solid #374151;
}

.task-checkbox {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  flex-shrink: 0;
}

.task-checkbox .completed {
  color: #10b981;
}

.task-checkbox .incomplete {
  color: #6b7280;
}

.task-checkbox:hover .incomplete {
  color: #9ca3af;
}

.task-content {
  flex: 1;
}

.task-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #ffffff;
}

.task-content p {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  color: #d1d5db;
}

.task-hint {
  font-size: 0.75rem;
  color: #9ca3af;
  font-style: italic;
}

.task-hint strong {
  color: #f59e0b;
  font-style: normal;
}

/* Code Examples */
.code-example {
  margin-bottom: 1.5rem;
  background: #2a2a2a;
  padding: 1.25rem;
  border-radius: 0.5rem;
  border: 1px solid #374151;
}

.code-example:last-child {
  margin-bottom: 0;
}

.code-example h4 {
  margin: 0 0 0.5rem 0;
  color: #3b82f6;
  font-size: 0.875rem;
  font-weight: 600;
}

.code-example p {
  margin: 0 0 0.75rem 0;
  font-size: 0.875rem;
  color: #d1d5db;
}

.code-block {
  background: #1e1e1e;
  border: 1px solid #333;
  border-radius: 0.375rem;
  padding: 1rem;
  overflow-x: auto;
  margin: 0;
}

.code-block code {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  color: #e5e7eb;
  white-space: pre;
}

/* Tips */
.tips-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.tip-item {
  position: relative;
  padding: 0.75rem 1rem;
  margin-bottom: 0.5rem;
  background: #2a2a2a;
  border-radius: 0.375rem;
  border-left: 3px solid #f59e0b;
  font-size: 0.875rem;
  color: #d1d5db;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-item::before {
  content: "💡";
  position: absolute;
  left: -0.5rem;
  top: 0.75rem;
  background: #2a2a2a;
  padding: 0 0.25rem;
}

/* Custom Scrollbar for Lesson Content */
.lesson-content::-webkit-scrollbar {
  width: 6px;
}

.lesson-content::-webkit-scrollbar-track {
  background: #1a1a1a;
  border-radius: 3px;
}

.lesson-content::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 3px;
}

.lesson-content::-webkit-scrollbar-thumb:hover {
  background: #505050;
}

/* Responsive */
@media (max-width: 768px) {
  .section-content {
    padding: 1rem;
  }

  .concepts-grid {
    grid-template-columns: 1fr;
  }

  .task-item {
    padding: 0.75rem;
  }

  .code-block {
    padding: 0.75rem;
  }

  .code-block code {
    font-size: 0.75rem;
  }

  .lesson-content::-webkit-scrollbar {
    width: 4px;
  }
}

/* Docs Content */
.docs-content h3 {
  margin-top: 0;
  color: #3b82f6;
  font-size: 1.25rem;
  margin-bottom: 1rem;
}

.docs-content p {
  margin-bottom: 1.5rem;
  color: #d1d5db;
}

.docs-content ul {
  list-style: none;
  padding: 0;
  display: grid;
  gap: 1rem;
}

.docs-content li {
  margin-bottom: 0;
}

.docs-content a {
  color: #60a5fa;
  text-decoration: none;
  padding: 1rem;
  display: block;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  background: #2a2a2a;
  border: 1px solid #333;
  position: relative;
  overflow: hidden;
}

.docs-content a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.docs-content a:hover::before {
  transform: translateX(0);
}

.docs-content a:hover {
  background: #3a3a3a;
  color: #ffffff;
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}
