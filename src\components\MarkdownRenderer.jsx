import React from 'react'
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter'
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism'

const MarkdownRenderer = ({ content }) => {
  if (!content) return null

  // Simple markdown parser for our specific needs
  const parseMarkdown = (text) => {
    const lines = text.split('\n')
    const elements = []
    let currentParagraph = []

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()

      // Empty line - end current paragraph
      if (line === '') {
        if (currentParagraph.length > 0) {
          elements.push({
            type: 'paragraph',
            content: currentParagraph.join(' ')
          })
          currentParagraph = []
        }
        continue
      }

      // Heading (## Title)
      if (line.startsWith('## ')) {
        // End current paragraph first
        if (currentParagraph.length > 0) {
          elements.push({
            type: 'paragraph',
            content: currentParagraph.join(' ')
          })
          currentParagraph = []
        }
        elements.push({
          type: 'heading',
          content: line.substring(3).trim()
        })
        continue
      }

      // Code snippet (💻 `code`)
      if (line.startsWith('💻 ')) {
        // End current paragraph first
        if (currentParagraph.length > 0) {
          elements.push({
            type: 'paragraph',
            content: currentParagraph.join(' ')
          })
          currentParagraph = []
        }
        // Extract code from backticks
        const codeMatch = line.match(/💻 `([^`]+)`(.*)/)
        if (codeMatch) {
          elements.push({
            type: 'code-snippet',
            code: codeMatch[1],
            comment: codeMatch[2].trim()
          })
        }
        continue
      }

      // Skip old-style code blocks (```javascript)
      if (line.startsWith('```')) {
        continue
      }

      // Bullet point (• Text)
      if (line.startsWith('• ')) {
        // End current paragraph first
        if (currentParagraph.length > 0) {
          elements.push({
            type: 'paragraph',
            content: currentParagraph.join(' ')
          })
          currentParagraph = []
        }
        elements.push({
          type: 'bullet',
          content: line.substring(2).trim()
        })
        continue
      }

      // Regular text - add to current paragraph
      currentParagraph.push(line)
    }

    // Add final paragraph if exists
    if (currentParagraph.length > 0) {
      elements.push({
        type: 'paragraph',
        content: currentParagraph.join(' ')
      })
    }

    return elements
  }

  // Process inline formatting (bold, em dash, inline code)
  const processInlineFormatting = (text) => {
    // Replace **bold** with <strong>
    text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')

    // Replace `code` with <code>
    text = text.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>')

    // Replace — with proper em dash
    text = text.replace(/—/g, '—')

    return text
  }

  const elements = parseMarkdown(content)

  return (
    <div className="markdown-content">
      {elements.map((element, index) => {
        // Only process inline formatting for elements that have content
        const processedContent = element.content ? processInlineFormatting(element.content) : ''

        switch (element.type) {
          case 'heading':
            return (
              <h3
                key={index}
                className="markdown-heading"
                dangerouslySetInnerHTML={{ __html: processedContent }}
              />
            )
          case 'code-snippet':
            return (
              <div key={index} className="tiny-code-section">
                <div className="tiny-code-block">
                  <SyntaxHighlighter
                    language="javascript"
                    style={vscDarkPlus}
                    customStyle={{
                      margin: 0,
                      padding: '6px 8px',
                      background: '#1a1a1a',
                      fontSize: '11px',
                      lineHeight: '1.3',
                      borderRadius: '4px',
                      border: '1px solid #333'
                    }}
                    showLineNumbers={false}
                    wrapLines={true}
                  >
                    {element.code}
                  </SyntaxHighlighter>
                </div>
                {element.comment && (
                  <span className="code-comment">{element.comment}</span>
                )}
              </div>
            )
          case 'bullet':
            return (
              <div
                key={index}
                className="markdown-bullet"
                dangerouslySetInnerHTML={{ __html: `• ${processedContent}` }}
              />
            )
          case 'paragraph':
            return (
              <p
                key={index}
                className="markdown-paragraph"
                dangerouslySetInnerHTML={{ __html: processedContent }}
              />
            )
          default:
            return null
        }
      })}
    </div>
  )
}

export default MarkdownRenderer
