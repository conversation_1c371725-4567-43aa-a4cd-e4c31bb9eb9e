.lesson {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background: #0a0a0a;
  color: #ffffff;
  overflow: hidden;
}

.lesson-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 1rem;
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid #333;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Lesson Header */
.lesson-header {
  background: #1a1a1a;
  border-bottom: 1px solid #333;
  padding: 1.5rem 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.lesson-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 100%;
}

.nav-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #2a2a2a;
  color: #ffffff;
  text-decoration: none;
  border-radius: 0.375rem;
  transition: background-color 0.2s;
  min-width: 100px;
  justify-content: center;
}

.nav-button:hover {
  background: #3a3a3a;
}

.lesson-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.lesson-info h1 {
  margin: 0;
  font-size: 2.25rem;
  text-align: center;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.difficulty {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.difficulty.beginner {
  background: #10b981;
  color: #ffffff;
}

.difficulty.intermediate {
  background: #f59e0b;
  color: #ffffff;
}

.difficulty.advanced {
  background: #ef4444;
  color: #ffffff;
}

/* Main Content */
.lesson-content {
  display: flex;
  height: 92vh;
  width: 100vw;
  gap: 1vw;
  padding: 1vh 1vw;
}

/* Content Panel - Constant Sidebar for Lesson Info */
.content-panel {
  width: 25vw;
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 0.75rem;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
  height: 100%;
}

.panel-tabs {
  display: flex;
  background: #2a2a2a;
  border-bottom: 1px solid #333;
  padding: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
}

.tab {
  flex: 1;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 0.5rem 0.5rem 0 0;
  position: relative;
  z-index: 11;
}

.tab:hover {
  color: #ffffff;
  background: #3a3a3a;
}

.tab.active {
  color: #3b82f6;
  background: #1a1a1a;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem 2rem;
  background: #1a1a1a;
  position: relative;
  z-index: 1;
}

/* Remove padding when showing learning cards for full height */
.panel-content.learning-cards-active {
  padding: 0;
  overflow: hidden;
}

/* Remove padding and let docs handle their own scrolling when docs tab is active */
.panel-content:not(.learning-cards-active) {
  padding: 0;
  overflow: hidden;
}

/* Custom Scrollbar for Panel Content */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: #1a1a1a;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #505050;
}

/* Editor Panel - 3/4 of screen, split vertically */
.editor-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #333; /* Gap color */
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
  position: relative;
}

.code-section {
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  border-radius: 0 0 0.5rem 0.5rem;
  overflow: hidden;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.2);
  min-height: 20%;
}

.code-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background: #2a2a2a;
  border-bottom: 1px solid #333;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.code-header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.code-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.code-actions {
  display: flex;
  gap: 0.75rem;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-button.reset {
  background: #374151;
  color: #ffffff;
}

.action-button.reset:hover:not(:disabled) {
  background: #4b5563;
}

.action-button.run {
  background: #10b981;
  color: #ffffff;
}

.action-button.run:hover:not(:disabled) {
  background: #059669;
}

.action-button.run.running {
  background: #f59e0b;
  animation: pulse-run 0.5s ease-in-out;
}

@keyframes pulse-run {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

.error-message {
  background: #7f1d1d;
  color: #fecaca;
  padding: 0.75rem 1rem;
  border-top: 1px solid #991b1b;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

/* Viewer Section - Top half */
.viewer-section {
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  border-radius: 0.5rem 0.5rem 0 0;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  min-height: 20%;
}

/* Resizer Handle */
.resizer {
  height: 8px;
  background: #333;
  cursor: ns-resize;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 10;
  transition: background-color 0.2s ease;
}

.resizer:hover {
  background: #404040;
}

.resizer.dragging {
  background: #3b82f6;
}

.resizer-line {
  width: 40px;
  height: 2px;
  background: #666;
  border-radius: 1px;
  transition: background-color 0.2s ease;
}

.resizer:hover .resizer-line {
  background: #888;
}

.resizer.dragging .resizer-line {
  background: #ffffff;
}

.viewer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background: #2a2a2a;
  border-bottom: 1px solid #333;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.viewer-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.run-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #9ca3af;
  font-size: 0.875rem;
  font-weight: 500;
}

.manual-run-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #9ca3af;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Docs Content in Lesson Sidebar */
.docs-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  height: 100%;
}

.docs-content h3 {
  margin: 0 0 0.5rem 0;
  color: #3b82f6;
  font-size: 1.2rem;
  font-weight: 600;
}

.docs-content p {
  margin: 0 0 1rem 0;
  color: #9ca3af;
  font-size: 0.875rem;
  line-height: 1.5;
}

.docs-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.doc-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #2a2a2a;
  border: 1px solid #333;
  border-radius: 0.5rem;
  text-decoration: none;
  color: #ffffff;
  transition: all 0.2s ease;
}

.doc-item:hover {
  background: #333;
  border-color: #3b82f6;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.doc-item div {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.doc-title {
  font-weight: 500;
  color: #ffffff;
  font-size: 0.875rem;
}

.doc-description {
  font-size: 0.75rem;
  color: #9ca3af;
  line-height: 1.3;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .lesson-header {
    padding: 1rem 1.5rem;
  }

  .content-panel {
    min-width: 280px;
  }

  .panel-content {
    padding: 1rem 1.5rem;
  }

  .viewer-header,
  .code-header {
    padding: 0.75rem 1rem;
  }
}

@media (max-width: 768px) {
  .lesson-content {
    flex-direction: column;
    height: 92vh;
    gap: 1vh;
    padding: 1vh;
  }

  .content-panel {
    width: 100vw;
    height: 30vh;
    border-radius: 0.75rem;
  }

  .editor-panel {
    height: 60vh;
    border-radius: 0.75rem;
  }

  .lesson-header {
    padding: 0.75rem 1rem;
  }

  .lesson-nav {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .nav-button {
    min-width: auto;
    width: 100%;
    justify-content: center;
  }

  .panel-content {
    padding: 1rem;
  }

  .viewer-header,
  .code-header {
    padding: 0.75rem 1rem;
  }

  .viewer-header h3,
  .code-header h3 {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .lesson-header {
    padding: 0.5rem 0.75rem;
  }

  .content-panel {
    height: 25vh;
  }

  .editor-panel {
    height: 65vh;
  }

  .panel-content {
    padding: 0.75rem;
  }

  .viewer-header,
  .code-header {
    padding: 0.5rem 0.75rem;
  }

  .tab {
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
  }

  .lesson-title {
    font-size: 1.25rem;
  }

  .lesson-description {
    font-size: 0.875rem;
  }
}
