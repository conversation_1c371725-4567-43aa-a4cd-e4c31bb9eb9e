.threejs-viewer {
  flex: 1;
  background: #1a1a1a;
  position: relative;
  overflow: hidden;
  height: 100%;
  width: 100%;
  min-height: 200px; /* Ensure minimum height */
}

.viewer-canvas {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 0 0 0.5rem 0.5rem;
  overflow: hidden;
}

.viewer-canvas canvas {
  display: block;
  width: 100% !important;
  height: 100% !important;
}

.viewer-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #ffffff;
  z-index: 10;
  background: rgba(26, 26, 26, 0.9);
  padding: 2rem;
  border-radius: 0.5rem;
  border: 1px solid #333;
}

.viewer-loading .loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid #333;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.viewer-loading span {
  font-size: 0.875rem;
  color: #9ca3af;
}

/* Error state styling */
.viewer-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(127, 29, 29, 0.9);
  color: #fecaca;
  padding: 1.5rem;
  border-radius: 0.5rem;
  border: 1px solid #991b1b;
  max-width: 80%;
  text-align: center;
  z-index: 10;
}

.viewer-error h3 {
  margin: 0 0 0.5rem 0;
  color: #ffffff;
}

.viewer-error p {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

/* Canvas controls overlay */
.viewer-controls {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
  z-index: 5;
}

.control-button {
  background: rgba(26, 26, 26, 0.8);
  border: 1px solid #333;
  color: #ffffff;
  padding: 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
  backdrop-filter: blur(4px);
}

.control-button:hover {
  background: rgba(42, 42, 42, 0.9);
  border-color: #3b82f6;
}

.control-button:active {
  transform: scale(0.95);
}

/* Fullscreen mode */
.viewer-canvas.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 1000;
  background: #1a1a1a;
}

/* Performance stats overlay */
.viewer-stats {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: rgba(26, 26, 26, 0.8);
  border: 1px solid #333;
  color: #ffffff;
  padding: 0.75rem;
  border-radius: 0.375rem;
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
  z-index: 5;
  backdrop-filter: blur(4px);
}

.viewer-stats div {
  margin-bottom: 0.25rem;
}

.viewer-stats div:last-child {
  margin-bottom: 0;
}

/* Mobile-First Responsive Design */

/* Tablets */
@media (max-width: 1024px) {
  .threejs-viewer {
    min-height: 250px;
  }

  .viewer-controls {
    top: 0.75rem;
    right: 0.75rem;
  }

  .viewer-stats {
    top: 0.75rem;
    left: 0.75rem;
    font-size: 0.7rem;
  }
}

/* Mobile landscape and small tablets */
@media (max-width: 768px) {
  .threejs-viewer {
    min-height: 200px;
  }

  .viewer-controls {
    top: 0.5rem;
    right: 0.5rem;
    gap: 0.25rem;
    flex-wrap: wrap;
  }

  .control-button {
    padding: 0.375rem;
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .viewer-stats {
    top: 0.5rem;
    left: 0.5rem;
    padding: 0.5rem;
    font-size: 0.625rem;
    max-width: 40%;
  }

  .viewer-loading {
    padding: 1.5rem;
    max-width: 90%;
  }

  .viewer-error {
    padding: 1rem;
    max-width: 90%;
    font-size: 0.875rem;
  }

  .viewer-context-lost {
    padding: 1.5rem;
    max-width: 90%;
  }
}

/* Mobile portrait */
@media (max-width: 480px) {
  .threejs-viewer {
    min-height: 180px;
  }

  .viewer-controls {
    top: 0.25rem;
    right: 0.25rem;
    gap: 0.125rem;
  }

  .control-button {
    padding: 0.25rem;
    min-height: 40px;
    min-width: 40px;
  }

  .viewer-stats {
    top: 0.25rem;
    left: 0.25rem;
    padding: 0.375rem;
    font-size: 0.55rem;
    max-width: 35%;
  }

  .viewer-stats div {
    margin-bottom: 0.125rem;
  }

  .viewer-loading {
    padding: 1rem;
    max-width: 95%;
  }

  .viewer-loading .loading-spinner {
    width: 1.5rem;
    height: 1.5rem;
  }

  .viewer-loading span {
    font-size: 0.8rem;
  }

  .viewer-error {
    padding: 0.75rem;
    max-width: 95%;
    font-size: 0.8rem;
  }

  .viewer-error h3 {
    font-size: 0.9rem;
  }

  .viewer-context-lost {
    padding: 1rem;
    max-width: 95%;
  }

  .viewer-context-lost h3 {
    font-size: 0.9rem;
  }

  .viewer-context-lost p {
    font-size: 0.8rem;
  }

  .viewer-context-lost button {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }
}

/* Very small screens */
@media (max-width: 360px) {
  .threejs-viewer {
    min-height: 150px;
  }

  .viewer-controls {
    top: 0.125rem;
    right: 0.125rem;
  }

  .control-button {
    padding: 0.125rem;
    min-height: 36px;
    min-width: 36px;
  }

  .viewer-stats {
    top: 0.125rem;
    left: 0.125rem;
    padding: 0.25rem;
    font-size: 0.5rem;
    max-width: 30%;
  }

  .viewer-loading {
    padding: 0.75rem;
  }

  .viewer-loading .loading-spinner {
    width: 1.25rem;
    height: 1.25rem;
  }

  .viewer-loading span {
    font-size: 0.75rem;
  }

  .viewer-error {
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  .viewer-error h3 {
    font-size: 0.85rem;
  }
}

/* Touch-friendly adjustments */
@media (max-width: 768px) and (pointer: coarse) {
  .control-button {
    min-height: 48px;
    min-width: 48px;
    padding: 0.5rem;
  }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .threejs-viewer {
    min-height: 150px;
  }

  .viewer-controls {
    flex-direction: column;
    gap: 0.125rem;
  }

  .viewer-stats {
    max-width: 25%;
  }
}

/* WebGL context lost handling */
.viewer-context-lost {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(127, 29, 29, 0.9);
  color: #fecaca;
  padding: 2rem;
  border-radius: 0.5rem;
  border: 1px solid #991b1b;
  text-align: center;
  z-index: 10;
}

.viewer-context-lost h3 {
  margin: 0 0 1rem 0;
  color: #ffffff;
}

.viewer-context-lost p {
  margin: 0 0 1rem 0;
}

.viewer-context-lost button {
  background: #3b82f6;
  color: #ffffff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.viewer-context-lost button:hover {
  background: #2563eb;
}

/* Execution flash effect */
.viewer-canvas.execution-flash {
  animation: execution-flash 0.5s ease-out;
}

@keyframes execution-flash {
  0% {
    box-shadow: inset 0 0 0 3px #10b981;
  }
  50% {
    box-shadow: inset 0 0 0 3px #10b981, 0 0 20px rgba(16, 185, 129, 0.5);
  }
  100% {
    box-shadow: inset 0 0 0 0px transparent;
  }
}
