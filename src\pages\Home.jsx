import { Link } from 'react-router-dom'
import { Play, Book, Code, Zap } from 'lucide-react'
import ThreeJSExamplesShowcase from '../components/ThreeJSExamplesShowcase'
import './Home.css'

const Home = () => {
  const features = [
    {
      icon: <Code size={32} />,
      title: "Interactive Code Editor",
      description: "Write and edit Three.js code with real-time preview and syntax highlighting."
    },
    {
      icon: <Play size={32} />,
      title: "Live Preview",
      description: "See your 3D scenes come to life instantly as you type."
    },
    {
      icon: <Book size={32} />,
      title: "Progressive Learning",
      description: "Start with basics and gradually advance to complex 3D graphics concepts."
    },
    {
      icon: <Zap size={32} />,
      title: "Hands-on Examples",
      description: "Learn by doing with practical examples and guided exercises."
    }
  ]



  return (
    <div className="home">
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-content">
          <h1 className="hero-title">
            Learn <span className="highlight">Three.js</span> Interactively
          </h1>
          <p className="hero-description">
            Master 3D web graphics with hands-on lessons, interactive code examples, 
            and real-time previews. From basic concepts to advanced techniques.
          </p>
          <div className="hero-actions">
            <Link to="/lesson/1" className="cta-button primary">
              Start Learning
              <Play size={20} />
            </Link>
            <a 
              href="https://threejs.org/docs/" 
              target="_blank" 
              rel="noopener noreferrer"
              className="cta-button secondary"
            >
              <Book size={20} />
              Documentation
            </a>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features">
        <div className="container">
          <h2>Why Choose Three.js Academy?</h2>
          <div className="features-grid">
            {features.map((feature, index) => (
              <div key={index} className="feature-card">
                <div className="feature-icon">
                  {feature.icon}
                </div>
                <h3>{feature.title}</h3>
                <p>{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 3D Examples Showcase */}
      <ThreeJSExamplesShowcase />

      {/* Learning Path Section */}
      <section className="learning-path">
        <div className="container">
          <h2>Your Learning Journey</h2>
          <div className="path-steps">
            <div className="path-step">
              <div className="step-number">1</div>
              <div className="step-content">
                <h3>Fundamentals</h3>
                <p>Scene, Camera, Renderer basics</p>
              </div>
            </div>
            <div className="path-connector"></div>
            <div className="path-step">
              <div className="step-number">2</div>
              <div className="step-content">
                <h3>3D Objects</h3>
                <p>Geometry, Materials, Meshes</p>
              </div>
            </div>
            <div className="path-connector"></div>
            <div className="path-step">
              <div className="step-number">3</div>
              <div className="step-content">
                <h3>Interactivity</h3>
                <p>Controls, Animations, Events</p>
              </div>
            </div>
            <div className="path-connector"></div>
            <div className="path-step">
              <div className="step-number">4</div>
              <div className="step-content">
                <h3>Advanced</h3>
                <p>Shaders, Physics, Optimization</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Home
