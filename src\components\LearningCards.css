/* Markdown Renderer Styles */
.markdown-content {
  line-height: 1.6;
}

.markdown-heading {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin: 16px 0 8px 0;
  padding: 0;
  border-bottom: 1px solid #333;
  padding-bottom: 4px;
}

.markdown-paragraph {
  font-size: 12px;
  color: #cccccc;
  margin: 8px 0;
  line-height: 1.5;
}

.markdown-bullet {
  font-size: 12px;
  color: #cccccc;
  margin: 6px 0;
  padding-left: 8px;
  line-height: 1.5;
}

.markdown-content strong {
  color: #ffffff;
  font-weight: 600;
}

.inline-code {
  background: #2a2a2a;
  color: #ffd700;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 11px;
  border: 1px solid #444;
}

/* SIMPLE DESKTOP LEARNING INTERFACE */
.compact-learning {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 1rem;
}

/* SIMPLE DESKTOP STEP LIST */
.step-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-shrink: 0;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: #2a2a2a;
  border: 1px solid #404040;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.step-item:hover {
  background: #333333;
  border-color: #555555;
}

.step-item.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: #ffffff;
}

.step-num {
  background: #4b5563;
  color: #ffffff;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
  flex-shrink: 0;
}

.step-item.active .step-num {
  background: #ffffff;
  color: #3b82f6;
}

.step-title {
  font-size: 0.9rem;
  font-weight: 500;
  color: #cccccc;
  flex: 1;
}

.step-item.active .step-title {
  color: #ffffff;
}

/* Hide mobile dropdown by default */
.step-dropdown {
  display: none;
}

/* SIMPLE DESKTOP STEP CONTENT */
.step-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #333;
}

.step-header h4 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #3b82f6;
  line-height: 1.3;
}

.step-type {
  background: #374151;
  color: #9ca3af;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.step-body {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.code-section {
  margin: 1rem 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.code-block {
  background: #0a0a0a;
  border: 1px solid #333;
  border-radius: 6px;
  overflow: hidden;
}

.run-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
  align-self: flex-start;
}

.run-btn:hover {
  background: #2563eb;
}

.run-btn:active {
  background: #1d4ed8;
}

.explanation {
  background: #1f2937;
  border: 1px solid #374151;
  border-radius: 6px;
  padding: 1rem;
  font-size: 0.875rem;
  color: #d1d5db;
  line-height: 1.6;
}

/* Scrollbar Styles */
.step-content::-webkit-scrollbar {
  width: 8px;
}

.step-content::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.step-content::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 4px;
}

.step-content::-webkit-scrollbar-thumb:hover {
  background: #555555;
}
