/* Markdown Renderer Styles */
.markdown-content {
  line-height: 1.6;
}

.markdown-heading {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin: 16px 0 8px 0;
  padding: 0;
  border-bottom: 1px solid #333;
  padding-bottom: 4px;
}

.markdown-paragraph {
  font-size: 12px;
  color: #cccccc;
  margin: 8px 0;
  line-height: 1.5;
}

.markdown-bullet {
  font-size: 12px;
  color: #cccccc;
  margin: 6px 0;
  padding-left: 8px;
  line-height: 1.5;
}

.markdown-content strong {
  color: #ffffff;
  font-weight: 600;
}

.inline-code {
  background: #2a2a2a;
  color: #ffd700;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 11px;
  border: 1px solid #444;
}

/* Ultra-compact learning interface */
.compact-learning {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: calc(90vh - 140px);
  font-size: 14px;
  overflow: hidden;
  flex: 1;
  min-height: 0;
  padding: 4px;
}

/* Step list - ultra compact */
.step-list {
  display: flex;
  flex-direction: column;
  gap: 1px;
  max-height: 29vh;
  overflow-y: auto;
  border-bottom: 1px solid #333;
  padding-bottom: 4px;
  margin-bottom: 4px;
  flex-shrink: 0;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 5px 8px;
  cursor: pointer;
  border-radius: 3px;
  transition: background-color 0.2s;
  font-size: 14px;
  line-height: 1.3;
}

.step-item:hover {
  background: #333;
}

.step-item.active {
  background: #3b82f6;
  color: white;
}

.step-num {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: #444;
  color: white;
  border-radius: 50%;
  font-size: 11px;
  font-weight: bold;
  flex-shrink: 0;
}

.step-item.active .step-num {
  background: white;
  color: #3b82f6;
}

.step-title {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}





/* Step content area */
.step-content {
  flex: 1;
  overflow-y: auto;
  padding: 4px;
  min-height: 0;
  max-height: 59vh;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  padding-bottom: 2px;
  border-bottom: 1px solid #333;
}

.step-header h4 {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  color: #3b82f6;
  line-height: 1.3;
}

.step-type {
  font-size: 11px;
  color: #888;
  text-transform: uppercase;
  background: #333;
  padding: 3px 6px;
  border-radius: 3px;
}

.step-body {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.step-desc {
  margin: 0;
  font-size: 13px;
  line-height: 1.4;
  color: #ddd;
}



/* Code section */
.code-section {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 4px;
  overflow: hidden;
  margin: 6px 0;
}

.code-block {
  background: #0a0a0a;
  overflow: hidden;
  max-height: 140px;
}

/* Override syntax highlighter styles */
.code-block pre {
  margin: 0 !important;
  padding: 8px !important;
  background: #0a0a0a !important;
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace !important;
  font-size: 11px !important;
  line-height: 1.4 !important;
}

.code-block code {
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace !important;
  font-size: 11px !important;
  line-height: 1.4 !important;
}

.run-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0 0 4px 4px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  width: 100%;
  transition: background-color 0.2s;
}

.run-btn:hover {
  background: #2563eb;
}

/* Explanation */
.explanation {
  background: rgba(59, 130, 246, 0.1);
  border-left: 2px solid #3b82f6;
  padding: 4px;
  font-size: 10px;
  line-height: 1.3;
  color: #ddd;
  border-radius: 2px;
}

/* Scrollbar for step content */
.step-content::-webkit-scrollbar {
  width: 3px;
}

.step-content::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.step-content::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 2px;
}

.step-list::-webkit-scrollbar {
  width: 3px;
}

.step-list::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.step-list::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 2px;
}

/* Tiny Code Section Styles */
.tiny-code-section {
  margin: 8px 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tiny-code-block {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 4px;
  overflow: hidden;
  max-width: 100%;
}

.tiny-code-block pre {
  margin: 0 !important;
  padding: 6px 8px !important;
  background: #1a1a1a !important;
  font-size: 11px !important;
  line-height: 1.3 !important;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace !important;
}

.code-comment {
  font-size: 10px;
  color: #888;
  font-style: italic;
  margin-left: 4px;
}

/* Ensure code doesn't break layout */
.tiny-code-section .token {
  font-size: 11px !important;
}

.tiny-code-section code {
  font-size: 11px !important;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace !important;
}
