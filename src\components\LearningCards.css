/* Markdown Renderer Styles */
.markdown-content {
  line-height: 1.6;
}

.markdown-heading {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin: 16px 0 8px 0;
  padding: 0;
  border-bottom: 1px solid #333;
  padding-bottom: 4px;
}

.markdown-paragraph {
  font-size: 12px;
  color: #cccccc;
  margin: 8px 0;
  line-height: 1.5;
}

.markdown-bullet {
  font-size: 12px;
  color: #cccccc;
  margin: 6px 0;
  padding-left: 8px;
  line-height: 1.5;
}

.markdown-content strong {
  color: #ffffff;
  font-weight: 600;
}

.inline-code {
  background: #2a2a2a;
  color: #ffd700;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 11px;
  border: 1px solid #444;
}

/* Ultra-compact learning interface */
.compact-learning {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: calc(90vh - 140px);
  font-size: 14px;
  overflow: hidden;
  flex: 1;
  min-height: 0;
  padding: 4px;
}

/* Step list - ultra compact */
.step-list {
  display: flex;
  flex-direction: column;
  gap: 1px;
  max-height: 29vh;
  overflow-y: auto;
  border-bottom: 1px solid #333;
  padding-bottom: 4px;
  margin-bottom: 4px;
  flex-shrink: 0;
}

/* Mobile Dropdown Styles */
.step-dropdown {
  position: relative;
  margin-bottom: 0.5rem;
  z-index: 100;
}

.dropdown-trigger {
  width: 100%;
  background: #2a2a2a;
  border: 1px solid #404040;
  border-radius: 6px;
  padding: 0.75rem;
  color: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;
  min-height: 44px; /* Touch-friendly */
  font-size: 0.85rem;
}

.dropdown-trigger:hover {
  background: #333333;
  border-color: #555555;
}

.dropdown-trigger:active {
  background: #1a1a1a;
}

.current-step {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  text-align: left;
}

.current-step .step-num {
  background: #3b82f6;
  color: #ffffff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
  flex-shrink: 0;
}

.current-step .step-title {
  font-size: 0.8rem;
  font-weight: 500;
  color: #ffffff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-icon {
  transition: transform 0.2s ease;
  color: #9ca3af;
  flex-shrink: 0;
}

.dropdown-icon.open {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #2a2a2a;
  border: 1px solid #404040;
  border-radius: 6px;
  margin-top: 0.25rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  z-index: 1000;
  max-height: 250px;
  overflow-y: auto;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #333333;
  min-height: 44px; /* Touch-friendly */
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background: #333333;
}

.dropdown-item.active {
  background: #3b82f6;
  color: #ffffff;
}

.dropdown-item.active:hover {
  background: #2563eb;
}

.dropdown-item .step-num {
  background: #4b5563;
  color: #ffffff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
  flex-shrink: 0;
}

.dropdown-item.active .step-num {
  background: #ffffff;
  color: #3b82f6;
}

.dropdown-item .step-title {
  font-size: 0.8rem;
  font-weight: 500;
  color: #cccccc;
  flex: 1;
}

.dropdown-item.active .step-title {
  color: #ffffff;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 5px 8px;
  cursor: pointer;
  border-radius: 3px;
  transition: background-color 0.2s;
  font-size: 14px;
  line-height: 1.3;
}

.step-item:hover {
  background: #333;
}

.step-item.active {
  background: #3b82f6;
  color: white;
}

.step-num {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: #444;
  color: white;
  border-radius: 50%;
  font-size: 11px;
  font-weight: bold;
  flex-shrink: 0;
}

.step-item.active .step-num {
  background: white;
  color: #3b82f6;
}

.step-title {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}





/* Step content area */
.step-content {
  flex: 1;
  overflow-y: auto;
  padding: 4px;
  min-height: 0;
  /* Remove fixed max-height on desktop - let content expand */
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  padding-bottom: 2px;
  border-bottom: 1px solid #333;
}

.step-header h4 {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  color: #3b82f6;
  line-height: 1.3;
}

.step-type {
  font-size: 11px;
  color: #888;
  text-transform: uppercase;
  background: #333;
  padding: 3px 6px;
  border-radius: 3px;
}

.step-body {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.step-desc {
  margin: 0;
  font-size: 13px;
  line-height: 1.4;
  color: #ddd;
}



/* Code section */
.code-section {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 4px;
  overflow: hidden;
  margin: 6px 0;
}

.code-block {
  background: #0a0a0a;
  overflow: hidden;
  max-height: 140px;
}

/* Override syntax highlighter styles */
.code-block pre {
  margin: 0 !important;
  padding: 8px !important;
  background: #0a0a0a !important;
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace !important;
  font-size: 11px !important;
  line-height: 1.4 !important;
}

.code-block code {
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace !important;
  font-size: 11px !important;
  line-height: 1.4 !important;
}

.run-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0 0 4px 4px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  width: 100%;
  transition: background-color 0.2s;
}

.run-btn:hover {
  background: #2563eb;
}

/* Explanation */
.explanation {
  background: rgba(59, 130, 246, 0.1);
  border-left: 2px solid #3b82f6;
  padding: 4px;
  font-size: 10px;
  line-height: 1.3;
  color: #ddd;
  border-radius: 2px;
}

/* Scrollbar for step content */
.step-content::-webkit-scrollbar {
  width: 3px;
}

.step-content::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.step-content::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 2px;
}

.step-list::-webkit-scrollbar {
  width: 3px;
}

.step-list::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.step-list::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 2px;
}

/* Tiny Code Section Styles */
.tiny-code-section {
  margin: 8px 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tiny-code-block {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 4px;
  overflow: hidden;
  max-width: 100%;
}

.tiny-code-block pre {
  margin: 0 !important;
  padding: 6px 8px !important;
  background: #1a1a1a !important;
  font-size: 11px !important;
  line-height: 1.3 !important;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace !important;
}

.code-comment {
  font-size: 10px;
  color: #888;
  font-style: italic;
  margin-left: 4px;
}

/* Ensure code doesn't break layout */
.tiny-code-section .token {
  font-size: 11px !important;
}

.tiny-code-section code {
  font-size: 11px !important;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace !important;
}

/* Mobile Responsive Styles for Dropdown */
@media (max-width: 768px) {
  /* Hide step list on mobile, show dropdown */
  .step-list {
    display: none;
  }

  .step-dropdown {
    display: block;
  }

  /* Ensure dropdown is above other content */
  .step-dropdown {
    z-index: 200;
  }

  .dropdown-menu {
    z-index: 1000;
  }

  /* Restore height constraints on mobile */
  .step-content {
    max-height: 35vh;
  }
}

@media (min-width: 769px) {
  /* Hide dropdown on desktop, show step list */
  .step-dropdown {
    display: none;
  }

  .step-list {
    display: flex;
  }

  /* Desktop - Allow step content to expand naturally */
  .step-content {
    max-height: none; /* Remove height constraint */
    flex: 1; /* Take available space */
  }

  .compact-learning {
    height: 100%; /* Fill parent container */
    display: flex;
    flex-direction: column;
  }
}

/* Mobile portrait adjustments */
@media (max-width: 480px) {
  .dropdown-trigger {
    padding: 0.625rem;
    font-size: 0.8rem;
    min-height: 48px; /* Larger touch target */
  }

  .current-step .step-num {
    width: 18px;
    height: 18px;
    font-size: 0.65rem;
  }

  .current-step .step-title {
    font-size: 0.75rem;
  }

  .dropdown-item {
    padding: 0.75rem;
    min-height: 48px; /* Larger touch target */
  }

  .dropdown-item .step-num {
    width: 18px;
    height: 18px;
    font-size: 0.65rem;
  }

  .dropdown-item .step-title {
    font-size: 0.75rem;
  }

  .dropdown-menu {
    max-height: 200px; /* Smaller on very small screens */
  }
}
