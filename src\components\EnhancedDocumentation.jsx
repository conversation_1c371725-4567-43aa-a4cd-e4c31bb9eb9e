import { useState } from 'react'
import {
  ExternalLink,
  Book,
  Code,
  Search,
  Play,
  FileText,
  Globe,
  Users,
  Lightbulb
} from 'lucide-react'
import './EnhancedDocumentation.css'

const EnhancedDocumentation = ({ lesson }) => {
  const [searchTerm, setSearchTerm] = useState('')

  // Generate contextual documentation based on lesson content
  const getContextualDocs = (lesson) => {
    const docs = []
    
    // Extract Three.js concepts from lesson
    if (lesson.concepts) {
      lesson.concepts.forEach(concept => {
        const conceptName = concept.name.toLowerCase()
        
        // Map concept names to Three.js documentation URLs
        const docMappings = {
          'scene': 'https://threejs.org/docs/#api/en/scenes/Scene',
          'perspectivecamera': 'https://threejs.org/docs/#api/en/cameras/PerspectiveCamera',
          'webglrenderer': 'https://threejs.org/docs/#api/en/renderers/WebGLRenderer',
          'mesh': 'https://threejs.org/docs/#api/en/objects/Mesh',
          'boxgeometry': 'https://threejs.org/docs/#api/en/geometries/BoxGeometry',
          'spheregeometry': 'https://threejs.org/docs/#api/en/geometries/SphereGeometry',
          'meshbasicmaterial': 'https://threejs.org/docs/#api/en/materials/MeshBasicMaterial',
          'meshstandardmaterial': 'https://threejs.org/docs/#api/en/materials/MeshStandardMaterial',
          'directionallight': 'https://threejs.org/docs/#api/en/lights/DirectionalLight',
          'ambientlight': 'https://threejs.org/docs/#api/en/lights/AmbientLight',
          'textureloader': 'https://threejs.org/docs/#api/en/loaders/TextureLoader',
          'group': 'https://threejs.org/docs/#api/en/objects/Group',
          'vector3': 'https://threejs.org/docs/#api/en/math/Vector3',
          'euler': 'https://threejs.org/docs/#api/en/math/Euler'
        }
        
        const url = docMappings[conceptName]
        if (url) {
          docs.push({
            title: concept.name,
            description: concept.description,
            url: url,
            type: 'api'
          })
        }
      })
    }
    
    return docs
  }

  const contextualDocs = getContextualDocs(lesson)

  const officialDocs = [
    {
      title: 'Three.js Documentation',
      description: 'Complete API reference for all Three.js classes and methods',
      url: 'https://threejs.org/docs/',
      type: 'reference'
    },
    {
      title: 'Three.js Examples',
      description: 'Interactive code examples showcasing Three.js capabilities',
      url: 'https://threejs.org/examples/',
      type: 'examples'
    },
    {
      title: 'Three.js Manual',
      description: 'Step-by-step tutorials and guides',
      url: 'https://threejs.org/manual/',
      type: 'tutorial'
    },
    {
      title: 'Three.js Fundamentals',
      description: 'Learn the fundamentals of Three.js development',
      url: 'https://threejs.org/fundamentals/',
      type: 'tutorial'
    }
  ]

  const communityResources = [
    {
      title: 'Three.js Journey',
      description: 'Comprehensive Three.js course by Bruno Simon',
      url: 'https://threejs-journey.com/',
      type: 'course'
    },
    {
      title: 'Three.js GitHub',
      description: 'Source code, issues, and discussions',
      url: 'https://github.com/mrdoob/three.js',
      type: 'source'
    },
    {
      title: 'Three.js Discord',
      description: 'Community chat and support',
      url: 'https://discord.gg/56GBJwAnUS',
      type: 'community'
    }
  ]

  const getIconForType = (type) => {
    switch (type) {
      case 'api':
        return <Code size={16} />
      case 'reference':
        return <Book size={16} />
      case 'examples':
        return <Play size={16} />
      case 'tutorial':
        return <FileText size={16} />
      case 'course':
        return <Book size={16} />
      case 'source':
        return <Code size={16} />
      case 'community':
        return <ExternalLink size={16} />
      default:
        return <ExternalLink size={16} />
    }
  }

  const filteredDocs = (docs) => {
    if (!searchTerm) return docs
    return docs.filter(doc => 
      doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.description.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }

  // Create all documentation items as cards with proper ordering
  const allDocItems = [
    // Lesson-specific docs (highest priority)
    ...contextualDocs.map((doc, index) => ({
      ...doc,
      category: 'Lesson-Specific APIs',
      icon: <Code size={20} />,
      categoryColor: '#3b82f6',
      priority: 1,
      order: index
    })),
    // Official docs (second priority)
    ...officialDocs.map((doc, index) => ({
      ...doc,
      category: 'Official Resources',
      icon: <Book size={20} />,
      categoryColor: '#10b981',
      priority: 2,
      order: index
    })),
    // Community resources (third priority)
    ...communityResources.map((doc, index) => ({
      ...doc,
      category: 'Community Resources',
      icon: <Users size={20} />,
      categoryColor: '#f59e0b',
      priority: 3,
      order: index
    })),
    // Additional resources (lowest priority)
    ...(lesson.docs || []).map((doc, index) => ({
      ...doc,
      category: 'Additional Resources',
      icon: <Lightbulb size={20} />,
      categoryColor: '#8b5cf6',
      priority: 4,
      order: index
    }))
  ]

  // Sort items by priority and then by order within category
  const sortedItems = allDocItems.sort((a, b) => {
    if (a.priority !== b.priority) {
      return a.priority - b.priority
    }
    return a.order - b.order
  })

  // Filter items based on search term
  const filteredItems = searchTerm.trim() === ''
    ? sortedItems
    : sortedItems.filter(item =>
        item.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.category?.toLowerCase().includes(searchTerm.toLowerCase())
      )

  return (
    <div className="enhanced-documentation">
      <div className="docs-header">
        <h3>Three.js Documentation</h3>
        <div className="search-box">
          <Search size={16} />
          <input
            type="text"
            placeholder="Search documentation..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <div className="docs-cards">
        {filteredItems.length > 0 ? (
          (() => {
            let currentCategory = null
            return filteredItems.map((item, index) => {
              const showCategoryHeader = item.category !== currentCategory
              currentCategory = item.category

              return (
                <div key={`${item.category}-${index}`}>
                  {showCategoryHeader && searchTerm.trim() === '' && (
                    <div className="category-header">
                      <div className="category-icon" style={{ backgroundColor: item.categoryColor }}>
                        {item.icon}
                      </div>
                      <h4 className="category-title">{item.category}</h4>
                    </div>
                  )}
                  <a
                    href={item.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="doc-card"
                  >
                    <div className="doc-card-header">
                      <div className="doc-card-icon" style={{ backgroundColor: item.categoryColor }}>
                        {item.icon}
                      </div>
                      <div className="doc-card-content">
                        <h4 className="doc-card-title">{item.title}</h4>
                        {searchTerm.trim() !== '' && (
                          <span className="doc-card-category">{item.category}</span>
                        )}
                      </div>
                      <ExternalLink size={16} className="doc-card-external" />
                    </div>
                    <div className="doc-card-description">
                      {item.description}
                    </div>
                  </a>
                </div>
              )
            })
          })()
        ) : (
          <div className="no-results">
            <Search size={24} />
            <p>No documentation found</p>
            <p className="no-results-hint">Try different search terms or clear the search.</p>
          </div>
        )}
      </div>

    </div>
  )
}

export default EnhancedDocumentation
