import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom'
import { ChevronLeft, ChevronRight, Play, RotateCcw, Book, Code, ExternalLink } from 'lucide-react'
import CodeEditor from '../components/CodeEditor'
import ThreeJSViewer from '../components/ThreeJSViewer'
import LearningCards from '../components/LearningCards'
import EnhancedDocumentation from '../components/EnhancedDocumentation'
import LoadingSpinner from '../components/LoadingSpinner'
import { getLessonData } from '../data/lessons'
import { markLessonStarted, saveLessonCode, getLessonCode } from '../utils/progress'
import './Lesson.css'

const Lesson = () => {
  const { lessonId } = useParams()
  const [lesson, setLesson] = useState(null)
  const [code, setCode] = useState('')
  const [isRunning, setIsRunning] = useState(false)
  const [error, setError] = useState(null)
  const [activeTab, setActiveTab] = useState('cards')
  const [codeVersion, setCodeVersion] = useState(1)
  const [viewerHeight, setViewerHeight] = useState(50) // Percentage of total height
  const [isDragging, setIsDragging] = useState(false)
  const [editorReloadKey, setEditorReloadKey] = useState(0)

  useEffect(() => {
    const lessonData = getLessonData(parseInt(lessonId))
    if (lessonData) {
      setLesson(lessonData)

      // Load saved code or use initial code
      const savedCode = getLessonCode(parseInt(lessonId))
      setCode(savedCode || lessonData.initialCode)

      setError(null)

      // Mark lesson as started
      markLessonStarted(parseInt(lessonId))

      // Auto-run the initial code once
      setTimeout(() => {
        console.log('🎯 Auto-running initial code...')
        setCodeVersion(prev => prev + 1)
      }, 1000) // Longer delay to ensure everything is mounted
    }
  }, [lessonId])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'Enter':
            event.preventDefault()
            handleRunCode()
            break
          case 'r':
            event.preventDefault()
            handleResetCode()
            break
          default:
            break
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [lesson])

  const handleCodeChange = (newCode) => {
    setCode(newCode)
    setError(null)

    // Auto-save code changes
    if (lesson) {
      saveLessonCode(lesson.id, newCode)
    }
  }

  const handleRunCode = () => {
    console.log('🔥 Run button clicked!')
    setIsRunning(true)
    setError(null)
    setCodeVersion(prev => {
      const newVersion = prev + 1
      console.log(`📈 Code version updated to: ${newVersion}`)
      return newVersion
    })
  }

  const handleResetCode = () => {
    if (lesson) {
      setCode(lesson.initialCode)
      setError(null)
    }
  }

  const handleCodeError = (errorMessage) => {
    console.log('❌ Code execution failed:', errorMessage)
    setError(errorMessage)
    setIsRunning(false)
  }

  const handleCodeSuccess = () => {
    console.log('✅ Code execution successful!')
    setIsRunning(false)
  }

  // Resizer drag handlers
  const handleMouseDown = (e) => {
    setIsDragging(true)
    e.preventDefault()
  }

  const handleMouseMove = (e) => {
    if (!isDragging) return

    const editorPanel = e.currentTarget.closest('.editor-panel')
    if (!editorPanel) return

    const rect = editorPanel.getBoundingClientRect()
    const y = e.clientY - rect.top
    const newHeight = Math.max(20, Math.min(80, (y / rect.height) * 100))

    setViewerHeight(newHeight)
  }

  const handleMouseUp = () => {
    setIsDragging(false)
    // Force editor reload with new dimensions
    setEditorReloadKey(prev => prev + 1)
    // Auto-run the code after resize
    setTimeout(() => {
      handleRunCode()
    }, 200) // Small delay to ensure editor has reloaded
  }

  // Add global mouse event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      const handleGlobalMouseMove = (e) => {
        const editorPanel = document.querySelector('.editor-panel')
        if (!editorPanel) return

        const rect = editorPanel.getBoundingClientRect()
        const y = e.clientY - rect.top
        const newHeight = Math.max(20, Math.min(80, (y / rect.height) * 100))

        setViewerHeight(newHeight)
      }

      const handleGlobalMouseUp = () => {
        setIsDragging(false)
        // Force editor reload with new dimensions
        setEditorReloadKey(prev => prev + 1)
        // Auto-run the code after resize
        setTimeout(() => {
          handleRunCode()
        }, 200) // Small delay to ensure editor has reloaded
      }

      document.addEventListener('mousemove', handleGlobalMouseMove)
      document.addEventListener('mouseup', handleGlobalMouseUp)

      return () => {
        document.removeEventListener('mousemove', handleGlobalMouseMove)
        document.removeEventListener('mouseup', handleGlobalMouseUp)
      }
    }
  }, [isDragging])

  const handleCardComplete = (cardIndex, card) => {
    // Save progress for this card
    console.log('Card completed:', card.title)
  }

  const handleCodeInsert = (codeToInsert) => {
    setCode(codeToInsert)
    setCodeVersion(prev => prev + 1)
  }

  if (!lesson) {
    return (
      <div className="lesson-loading">
        <LoadingSpinner size="large" text="Loading lesson..." />
      </div>
    )
  }

  const prevLessonId = parseInt(lessonId) > 1 ? parseInt(lessonId) - 1 : null
  const nextLessonId = parseInt(lessonId) < 11 ? parseInt(lessonId) + 1 : null

  return (
    <div className="lesson">
      {/* Lesson Header */}
      <div className="lesson-header">
        <div className="lesson-nav">
          {prevLessonId && (
            <Link to={`/lesson/${prevLessonId}`} className="nav-button">
              <ChevronLeft size={20} />
              Previous
            </Link>
          )}
          <div className="lesson-info">
            <h1>{lesson.title}</h1>
            <span className={`difficulty ${lesson.difficulty.toLowerCase()}`}>
              {lesson.difficulty}
            </span>
          </div>
          {nextLessonId && (
            <Link to={`/lesson/${nextLessonId}`} className="nav-button">
              Next
              <ChevronRight size={20} />
            </Link>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="lesson-content">
        {/* Left Panel - Lesson Content */}
        <div className="content-panel">
          <div className="panel-tabs">
            <button
              className={`tab ${activeTab === 'cards' ? 'active' : ''}`}
              onClick={() => setActiveTab('cards')}
            >
              <Book size={16} />
              Learning Steps
            </button>
            <button
              className={`tab ${activeTab === 'docs' ? 'active' : ''}`}
              onClick={() => setActiveTab('docs')}
            >
              <ExternalLink size={16} />
              Three.js Docs
            </button>
          </div>
          <div className={`panel-content ${activeTab === 'cards' ? 'learning-cards-active' : ''}`}>
            {activeTab === 'cards' ? (
              <LearningCards
                lesson={lesson}
                onCodeInsert={handleCodeInsert}
              />
            ) : (
              <EnhancedDocumentation lesson={lesson} />
            )}
          </div>
        </div>

        {/* Right Panel - 3D Viewer and Code Editor */}
        <div className="editor-panel">
          {/* Three.js Viewer - Top Half */}
          <div
            className="viewer-section"
            style={{ height: `${viewerHeight}%` }}
          >
            <div className="viewer-header">
              <h3>3D Preview</h3>
              <div className="run-indicator">
                <span>Press "Run Now" to execute</span>
              </div>
            </div>
            <ThreeJSViewer
              code={code}
              codeVersion={codeVersion}
              onError={handleCodeError}
              onSuccess={handleCodeSuccess}
            />
          </div>

          {/* Resizer Handle */}
          <div
            className={`resizer ${isDragging ? 'dragging' : ''}`}
            onMouseDown={handleMouseDown}
          >
            <div className="resizer-line"></div>
          </div>

          {/* Code Editor - Bottom Half */}
          <div
            className="code-section"
            style={{ height: `${100 - viewerHeight}%` }}
          >
            <div className="code-header">
              <div className="code-header-left">
                <h3>Code Editor</h3>
                <div className="manual-run-indicator">
                  <span>Manual execution</span>
                </div>
              </div>
              <div className="code-actions">
                <button
                  className="action-button reset"
                  onClick={handleResetCode}
                  title="Reset to initial code"
                >
                  <RotateCcw size={16} />
                  Reset
                </button>
                <button
                  className={`action-button run ${isRunning ? 'running' : ''}`}
                  onClick={handleRunCode}
                  disabled={isRunning}
                  title="Execute the code in the 3D viewer"
                >
                  <Play size={16} />
                  {isRunning ? 'Running...' : 'Run'}
                </button>
              </div>
            </div>
            <CodeEditor
              value={code}
              onChange={handleCodeChange}
              language="javascript"
              forceReload={editorReloadKey}
            />
            {error && (
              <div className="error-message">
                <strong>Error:</strong> {error}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default Lesson
