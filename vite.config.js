import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    host: true,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        configure: (proxy, options) => {
          // Fallback to mock if local API server is not running
          proxy.on('error', (err, req, res) => {
            console.log('API proxy error, using mock response')
            res.writeHead(200, { 'Content-Type': 'application/json' })
            res.end(JSON.stringify({
              lesson: {
                title: "Mock Lesson",
                description: "This is a mock lesson because the API server is not running",
                code: "// Mock lesson code\nconst geometry = new THREE.BoxGeometry(1, 1, 1);\nconst material = new THREE.MeshStandardMaterial({ color: 0x0066ff });\nconst cube = createMesh(geometry, material);\nscene.add(cube);\n\n// Add lighting\nconst ambientLight = new THREE.AmbientLight(0x404040, 0.4);\nscene.add(ambientLight);\n\nconst directionalLight = new THREE.DirectionalLight(0xffffff, 1);\ndirectionalLight.position.set(5, 5, 5);\nscene.add(directionalLight);\n\n// Position camera\ncamera.position.set(3, 3, 5);\ncamera.lookAt(0, 0, 0);\n\n// Animation\nanimate(() => {\n  cube.rotation.x += 0.01;\n  cube.rotation.y += 0.01;\n});",
                steps: [
                  {
                    title: "Step 1: Basic Setup",
                    content: "This is a mock lesson for local development! 🎯\n\n🏗️ **SCENE ELEMENTS:**\n📦 Every scene needs geometry (shape)\n🎨 Materials define appearance\n💡 Lighting makes objects visible\n⚡ Animation brings everything to life\n💻 `createMesh(geometry, material)` // helper function\n\n🎯 **WHY THIS WORKS:**\n✨ Follows the magic formula: Geometry + Material = Mesh\n🌟 Standard workflow for all 3D objects",
                    codeExample: "const geometry = new THREE.BoxGeometry(1, 1, 1);\nconst material = new THREE.MeshStandardMaterial({ color: 0x0066ff });\nconst cube = createMesh(geometry, material);\nscene.add(cube);",
                    explanation: "This creates a basic 3D object and adds it to the scene using the fundamental Three.js pattern."
                  }
                ],
                concepts: [
                  {
                    name: "Basic 3D Scene",
                    description: "Understanding the fundamental components of a Three.js scene"
                  }
                ]
              }
            }))
          })
        }
      }
    }
  }
})
