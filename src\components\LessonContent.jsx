import { useState } from 'react'
import { ChevronDown, ChevronRight, CheckCircle, Circle } from 'lucide-react'
import './LessonContent.css'

const LessonContent = ({ lesson }) => {
  const [expandedSections, setExpandedSections] = useState(new Set(['overview']))
  const [completedTasks, setCompletedTasks] = useState(new Set())

  const toggleSection = (sectionId) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId)
    } else {
      newExpanded.add(sectionId)
    }
    setExpandedSections(newExpanded)
  }

  const toggleTask = (taskId) => {
    const newCompleted = new Set(completedTasks)
    if (newCompleted.has(taskId)) {
      newCompleted.delete(taskId)
    } else {
      newCompleted.add(taskId)
    }
    setCompletedTasks(newCompleted)
  }

  if (!lesson) {
    return <div className="lesson-content-loading">Loading lesson content...</div>
  }

  return (
    <div className="lesson-content">
      {/* Overview Section */}
      <div className="content-section">
        <button
          className="section-header"
          onClick={() => toggleSection('overview')}
        >
          {expandedSections.has('overview') ? (
            <ChevronDown size={16} />
          ) : (
            <ChevronRight size={16} />
          )}
          <h3>Overview</h3>
        </button>
        {expandedSections.has('overview') && (
          <div className="section-content">
            <p>{lesson.description}</p>
            {lesson.objectives && (
              <div className="objectives">
                <h4>Learning Objectives:</h4>
                <ul>
                  {lesson.objectives.map((objective, index) => (
                    <li key={index}>{objective}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Theory Section */}
      {lesson.theory && (
        <div className="content-section">
          <button
            className="section-header"
            onClick={() => toggleSection('theory')}
          >
            {expandedSections.has('theory') ? (
              <ChevronDown size={16} />
            ) : (
              <ChevronRight size={16} />
            )}
            <h3>Theory</h3>
          </button>
          {expandedSections.has('theory') && (
            <div className="section-content">
              {lesson.theory.map((paragraph, index) => (
                <p key={index}>{paragraph}</p>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Key Concepts Section */}
      {lesson.concepts && (
        <div className="content-section">
          <button
            className="section-header"
            onClick={() => toggleSection('concepts')}
          >
            {expandedSections.has('concepts') ? (
              <ChevronDown size={16} />
            ) : (
              <ChevronRight size={16} />
            )}
            <h3>Key Concepts</h3>
          </button>
          {expandedSections.has('concepts') && (
            <div className="section-content">
              <div className="concepts-grid">
                {lesson.concepts.map((concept, index) => (
                  <div key={index} className="concept-card">
                    <h4>{concept.name}</h4>
                    <p>{concept.description}</p>
                    {concept.example && (
                      <div className="concept-example">
                        <strong>Example:</strong>
                        <code>{concept.example}</code>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Tasks Section */}
      {lesson.tasks && (
        <div className="content-section">
          <button
            className="section-header"
            onClick={() => toggleSection('tasks')}
          >
            {expandedSections.has('tasks') ? (
              <ChevronDown size={16} />
            ) : (
              <ChevronRight size={16} />
            )}
            <h3>Tasks</h3>
          </button>
          {expandedSections.has('tasks') && (
            <div className="section-content">
              <div className="tasks-list">
                {lesson.tasks.map((task, index) => (
                  <div key={index} className="task-item">
                    <button
                      className="task-checkbox"
                      onClick={() => toggleTask(index)}
                    >
                      {completedTasks.has(index) ? (
                        <CheckCircle size={20} className="completed" />
                      ) : (
                        <Circle size={20} className="incomplete" />
                      )}
                    </button>
                    <div className="task-content">
                      <h4>{task.title}</h4>
                      <p>{task.description}</p>
                      {task.hint && (
                        <div className="task-hint">
                          <strong>Hint:</strong> {task.hint}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Code Examples Section */}
      {lesson.examples && (
        <div className="content-section">
          <button
            className="section-header"
            onClick={() => toggleSection('examples')}
          >
            {expandedSections.has('examples') ? (
              <ChevronDown size={16} />
            ) : (
              <ChevronRight size={16} />
            )}
            <h3>Code Examples</h3>
          </button>
          {expandedSections.has('examples') && (
            <div className="section-content">
              {lesson.examples.map((example, index) => (
                <div key={index} className="code-example">
                  <h4>{example.title}</h4>
                  <p>{example.description}</p>
                  <pre className="code-block">
                    <code>{example.code}</code>
                  </pre>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Tips Section */}
      {lesson.tips && (
        <div className="content-section">
          <button
            className="section-header"
            onClick={() => toggleSection('tips')}
          >
            {expandedSections.has('tips') ? (
              <ChevronDown size={16} />
            ) : (
              <ChevronRight size={16} />
            )}
            <h3>Tips & Best Practices</h3>
          </button>
          {expandedSections.has('tips') && (
            <div className="section-content">
              <ul className="tips-list">
                {lesson.tips.map((tip, index) => (
                  <li key={index} className="tip-item">{tip}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default LessonContent
