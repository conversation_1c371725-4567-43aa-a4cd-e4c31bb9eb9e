import { useState, useEffect } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { Menu, X, Book, Code, Play, CheckCircle } from 'lucide-react'
import HelpSystem from './HelpSystem'
import { isLessonCompleted, getCompletionPercentage } from '../utils/progress'
import './Layout.css'

const Layout = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [completionPercentage, setCompletionPercentage] = useState(0)
  const location = useLocation()

  useEffect(() => {
    setCompletionPercentage(getCompletionPercentage())
  }, [location.pathname])

  const lessons = [
    { id: 1, title: "Three.js Fundamentals", difficulty: "Beginner", category: "Fundamentals" },
    { id: 2, title: "Core Architecture", difficulty: "Beginner", category: "Fundamentals" },
    { id: 3, title: "Geometry & Materials", difficulty: "Beginner", category: "Fundamentals" },
    { id: 4, title: "Lighting & Shadows", difficulty: "Beginner", category: "Fundamentals" },
    { id: 5, title: "Texturing & Environment", difficulty: "Intermediate", category: "Materials" },
    { id: 6, title: "Animation Fundamentals", difficulty: "Intermediate", category: "Animation" },
    { id: 7, title: "User Interaction & Controls", difficulty: "Intermediate", category: "Interaction" },
    { id: 8, title: "Importing 3D Assets", difficulty: "Intermediate", category: "Assets" },
    { id: 9, title: "Post-Processing & Effects", difficulty: "Advanced", category: "Effects" },
    { id: 10, title: "Advanced Shaders", difficulty: "Advanced", category: "Advanced" },
    { id: 11, title: "Creating a Galaxy", difficulty: "Intermediate", category: "Creative Projects" },
    { id: 12, title: "AI Lesson Generator", difficulty: "Variable", category: "AI-Powered" },
  ]

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'Beginner': return 'text-green-500'
      case 'Intermediate': return 'text-yellow-500'
      case 'Advanced': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }

  return (
    <div className="layout">
      {/* Fixed Burger Menu - Top Left Corner */}
      <button
        className="sidebar-toggle"
        onClick={() => setSidebarOpen(!sidebarOpen)}
      >
        {sidebarOpen ? <X size={20} /> : <Menu size={20} />}
      </button>

      {/* Header */}
      <header className="header">
        <div className="header-content">
          <Link to="/" className="logo">
            <Code size={28} />
            <span>Three.js Academy</span>
          </Link>
        </div>
      </header>

      {/* Main Layout Container */}
      <div className="main-layout">
        {/* Sidebar - Lessons Menu (controlled by burger menu) */}
        {sidebarOpen && (
          <>
            <div className="sidebar-overlay" onClick={() => setSidebarOpen(false)}></div>
            <aside className="sidebar">
              <div className="sidebar-header">
                <h3 className="sidebar-title">Lessons</h3>
                <button
                  className="sidebar-close"
                  onClick={() => setSidebarOpen(false)}
                >
                  <X size={20} />
                </button>
              </div>
              <div className="sidebar-content">
                <div className="progress-section">
                  <h3>Progress</h3>
                  <div className="progress-bar">
                    <div
                      className="progress-fill"
                      style={{ width: `${completionPercentage}%` }}
                    ></div>
                  </div>
                  <span className="progress-text">{completionPercentage}% Complete</span>
                </div>

                <div className="lessons-section">
                  <h3>Lessons</h3>
                  <div className="lessons-list">
                    {lessons.map((lesson) => (
                      <Link
                        key={lesson.id}
                        to={`/lesson/${lesson.id}`}
                        className={`lesson-item ${
                          location.pathname === `/lesson/${lesson.id}` ? 'active' : ''
                        } ${isLessonCompleted(lesson.id) ? 'completed' : ''}`}
                        onClick={() => setSidebarOpen(false)} // Close sidebar when lesson is selected
                      >
                        <div className="lesson-info">
                          <span className="lesson-title">
                            {lesson.title}
                            {isLessonCompleted(lesson.id) && (
                              <CheckCircle size={16} className="completion-icon" />
                            )}
                          </span>
                          <span className="lesson-category">{lesson.category}</span>
                        </div>
                        <span className={`lesson-difficulty ${getDifficultyColor(lesson.difficulty)}`}>
                          {lesson.difficulty}
                        </span>
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            </aside>
          </>
        )}

        {/* Main Content */}
        <main className="main-content">
          {children}
        </main>
      </div>



      {/* Help System */}
      <HelpSystem />
    </div>
  )
}

export default Layout
