{"name": "threejs-academy", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "client": "vite", "server": "node server.js", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@codemirror/lang-javascript": "^6.2.4", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.3", "@codemirror/view": "^6.38.0", "@monaco-editor/react": "^4.7.0", "@types/three": "^0.178.1", "concurrently": "^8.2.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.2", "lucide-react": "^0.525.0", "openai": "^5.9.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "react-syntax-highlighter": "^15.6.1", "three": "^0.178.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.4"}}