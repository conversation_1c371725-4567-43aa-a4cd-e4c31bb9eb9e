/* Examples Showcase Section */
.examples-showcase {
  padding: 4rem 0;
  background: #111111;
}

.examples-showcase h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #ffffff;
}

.examples-showcase > .container > p {
  text-align: center;
  color: #9ca3af;
  margin-bottom: 3rem;
  font-size: 1.125rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.examples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2.5rem;
  max-width: 1400px;
  margin: 0 auto;
}

.example-card {
  background: linear-gradient(145deg, #1a1a1a 0%, #2a2a2a 100%);
  border-radius: 1rem;
  border: 1px solid #333;
  overflow: hidden;
  transition: all 0.4s ease;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.example-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 1;
}

.example-card:hover::before {
  opacity: 1;
}

.example-card:hover {
  transform: translateY(-8px) scale(1.02);
  border-color: #3b82f6;
  box-shadow: 0 12px 40px rgba(59, 130, 246, 0.3);
}

.example-preview {
  width: 100%;
  height: 320px;
  position: relative;
  background: radial-gradient(circle at center, #0a0a0a 0%, #000000 100%);
  overflow: hidden;
  border-bottom: 1px solid #333;
}

.example-preview canvas {
  width: 100% !important;
  height: 100% !important;
  display: block;
  transition: filter 0.3s ease;
}

.example-card:hover .example-preview canvas {
  filter: brightness(1.1) contrast(1.05);
}

.example-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #9ca3af;
  z-index: 10;
}

.example-loading .loading-spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid #333;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.example-loading span {
  font-size: 0.875rem;
  color: #6b7280;
}

.example-content {
  padding: 2rem;
  background: linear-gradient(145deg, #1a1a1a 0%, #1f1f1f 100%);
}

.example-content h3 {
  font-size: 1.4rem;
  margin-bottom: 0.75rem;
  color: #ffffff;
  font-weight: 700;
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.example-content p {
  color: #cccccc;
  line-height: 1.7;
  margin: 0;
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .examples-showcase {
    padding: 3rem 0;
  }
  
  .examples-showcase h2 {
    font-size: 2rem;
  }
  
  .examples-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0 1rem;
  }
  
  .example-card {
    margin: 0;
  }
  
  .example-preview {
    height: 200px;
  }
  
  .example-content {
    padding: 1.25rem;
  }
  
  .example-content h3 {
    font-size: 1.125rem;
  }
  
  .example-content p {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .examples-showcase {
    padding: 2rem 0;
  }
  
  .examples-showcase h2 {
    font-size: 1.75rem;
  }
  
  .examples-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0 0.75rem;
  }
  
  .example-preview {
    height: 180px;
  }
  
  .example-content {
    padding: 1rem;
  }
}

/* Performance optimizations */
.example-card {
  will-change: transform;
}

.example-preview {
  contain: layout style paint;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .example-card {
    transition: none;
  }
  
  .example-card:hover {
    transform: none;
  }
  
  .loading-spinner {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .example-card {
    border-color: #666;
  }
  
  .example-card:hover {
    border-color: #fff;
  }
  
  .example-content h3 {
    color: #fff;
  }
  
  .example-content p {
    color: #ccc;
  }
}
