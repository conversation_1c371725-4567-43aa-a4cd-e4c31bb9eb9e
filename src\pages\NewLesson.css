.new-lesson {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #0a0a0a;
  color: #ffffff;
  overflow: hidden;
}

/* <PERSON><PERSON> Header */
.lesson-header {
  background: #1a1a1a;
  border-bottom: 1px solid #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.lesson-header.desktop {
  padding: 1rem 2rem;
}

.lesson-header.mobile {
  padding: 0.375rem 0.75rem;
}

.lesson-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1400px;
  margin: 0 auto;
}

.nav-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #2a2a2a;
  color: #ffffff;
  text-decoration: none;
  border-radius: 0.5rem;
  border: 1px solid #404040;
  transition: all 0.2s ease;
  font-weight: 500;
}

.nav-button:hover {
  background: #3a3a3a;
  border-color: #3b82f6;
}

.lesson-info {
  text-align: center;
  flex: 1;
  margin: 0 2rem;
}

.lesson-info h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.75rem;
  font-weight: 700;
  color: #f9fafb;
}

.lesson-meta {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.difficulty {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.difficulty.beginner { background: #065f46; color: #10b981; }
.difficulty.intermediate { background: #92400e; color: #f59e0b; }
.difficulty.advanced { background: #991b1b; color: #ef4444; }

.progress-text {
  color: #9ca3af;
  font-size: 0.875rem;
}

/* Mobile Header Styles */
.mobile-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 32px;
  flex-wrap: nowrap; /* Prevent wrapping */
  width: 100%;
}

.mobile-nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #2a2a2a;
  border: 1px solid #404040;
  border-radius: 6px;
  color: #9ca3af;
  text-decoration: none;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.mobile-nav-btn:hover {
  background: #333333;
  color: #ffffff;
  border-color: #555555;
}

.mobile-nav-btn:active {
  background: #1a1a1a;
  transform: scale(0.95);
}

.mobile-lesson-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  min-width: 0;
  justify-content: center;
  flex-wrap: nowrap; /* Keep title and difficulty on same line */
  overflow: hidden; /* Prevent overflow */
}

.mobile-title {
  color: #ffffff;
  font-size: 0.8rem;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
  max-width: none; /* Remove max-width constraint */
}

.mobile-difficulty {
  font-size: 0.65rem;
  font-weight: 500;
  padding: 0.125rem 0.375rem;
  border-radius: 12px;
  white-space: nowrap;
  flex-shrink: 0;
}

.mobile-difficulty.beginner { background: #065f46; color: #10b981; }
.mobile-difficulty.intermediate { background: #92400e; color: #f59e0b; }
.mobile-difficulty.advanced { background: #991b1b; color: #ef4444; }

/* Hide mobile header by default - desktop first */
.lesson-header.mobile {
  display: none;
}

.lesson-header.desktop {
  display: block;
}

/* Desktop layout - ABSOLUTELY NO OVERFLOW */
@media (min-width: 1025px) {
  .lesson-content {
    /* Very conservative height - ensure NO overflow */
    height: calc(100vh - 200px) !important;
    min-height: calc(100vh - 200px) !important;
    max-height: calc(100vh - 200px) !important;
    overflow: hidden !important;
    margin-bottom: 0 !important;
    position: relative !important;
    box-sizing: border-box !important;
  }

  .learning-column,
  .editor-column {
    height: 100% !important;
    max-height: 100% !important;
    min-height: 100% !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
  }

  .editor-panel {
    height: 100% !important;
    max-height: 100% !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
  }

  .viewer-section,
  .code-section {
    /* Allow resizer to work - don't override flex or height */
    overflow: hidden !important;
    box-sizing: border-box !important;
    min-height: 20% !important; /* Minimum usable size */
    max-height: 80% !important; /* Maximum usable size */
  }
}

/* Main Content - Two Column Layout - ABSOLUTELY NO OVERFLOW */
.lesson-content {
  display: flex;
  flex: 1;
  max-width: none;
  margin: 0 10vw 0 10vw;
  width: auto;
  gap: 1rem;
  padding: 1rem;
  /* Very conservative height to ensure NO overflow */
  height: calc(100vh - 200px);
  min-height: calc(100vh - 200px);
  max-height: calc(100vh - 200px);
  overflow: hidden;
  /* Ensure this div never goes beyond viewport */
  position: relative;
  box-sizing: border-box;
}

/* Left Column - Learning Steps */
.learning-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  border-radius: 0.75rem;
  border: 1px solid #333;
  height: 100%;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Collapsed state for mobile */
.learning-column.collapsed {
  height: auto;
  min-height: auto;
  max-height: none;
  flex: none;
}

.learning-column.collapsed .learning-header {
  border-bottom: none;
}

.learning-column.collapsed .learning-tabs {
  border-radius: 0.75rem;
}

.learning-header {
  border-bottom: 1px solid #333;
  background: #1f2937;
}

.learning-tabs {
  display: flex;
  background: #2a2a2a;
  position: relative;
}

.learning-collapse-btn {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: #374151;
  border: 1px solid #4b5563;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 36px;
  min-width: 36px;
}

.learning-collapse-btn:hover {
  background: #4b5563;
  color: #ffffff;
  border-color: #6b7280;
}

.learning-collapse-btn:active {
  transform: translateY(-50%) scale(0.95);
}

/* Smooth transitions for collapse/expand */
.tab-content {
  transition: all 0.3s ease;
  overflow: hidden;
}

.tab-description {
  transition: all 0.3s ease;
  overflow: hidden;
}

.learning-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.learning-tab:hover {
  color: #d1d5db;
  background: #374151;
}

.learning-tab.active {
  color: #3b82f6;
  background: #1f2937;
  border-bottom-color: #3b82f6;
}

.tab-description {
  padding: 1rem 1.5rem;
}

.tab-description p {
  margin: 0;
  color: #9ca3af;
  font-size: 0.875rem;
}

.tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow-y: auto;
}

.steps-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  scrollbar-width: thin;
  scrollbar-color: #4b5563 #1f2937;
  min-height: 0;
  height: 0;
}

.docs-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
  min-height: 0;
  height: 0;
}

.steps-container::-webkit-scrollbar {
  width: 8px;
}

.steps-container::-webkit-scrollbar-track {
  background: #1f2937;
  border-radius: 4px;
}

.steps-container::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

.steps-container::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Learning Steps */
.learning-step {
  background: #1f2937;
  border: 1px solid #374151;
  border-radius: 0.75rem;
  margin-bottom: 1.5rem;
  overflow: hidden;
  transition: all 0.3s ease;
}

.learning-step.active {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

.learning-step.completed {
  border-color: #10b981;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #2d3748;
  border-bottom: 1px solid #374151;
}

.step-toggle {
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  transition: color 0.2s ease;
  padding: 0;
  display: flex;
  align-items: center;
}

.step-toggle:hover {
  color: #9ca3af;
}

.learning-step.completed .step-toggle {
  color: #10b981;
}

.step-info {
  flex: 1;
}

.step-info h3 {
  margin: 0 0 0.25rem 0;
  color: #f9fafb;
  font-weight: 600;
  font-size: 1rem;
}

.step-type {
  color: #9ca3af;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.step-number {
  background: #374151;
  color: #9ca3af;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.learning-step.active .step-number {
  background: #3b82f6;
  color: white;
}

.learning-step.completed .step-number {
  background: #10b981;
  color: white;
}

.step-content {
  padding: 1.5rem;
}

.step-content p {
  color: #e5e7eb;
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
}

.code-example {
  background: #0a0a0a;
  border: 1px solid #333;
  border-radius: 0.5rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.code-example h4 {
  margin: 0;
  padding: 0.75rem 1rem;
  background: #1a1a1a;
  color: #10b981;
  font-size: 0.875rem;
  font-weight: 600;
  border-bottom: 1px solid #333;
}

.code-example pre {
  margin: 0;
  padding: 1rem;
  background: #000;
  overflow-x: auto;
}

.code-example code {
  color: #d1d5db;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.insert-code-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-top: 1px solid #333;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  width: 100%;
  transition: background-color 0.2s ease;
}

.insert-code-btn:hover {
  background: #2563eb;
}

.explanation {
  background: rgba(59, 130, 246, 0.1);
  border-radius: 0.5rem;
  padding: 1rem;
  border-left: 3px solid #3b82f6;
  margin-bottom: 1.5rem;
}

.explanation h4 {
  margin: 0 0 0.5rem 0;
  color: #3b82f6;
  font-size: 0.875rem;
  font-weight: 600;
}

.explanation p {
  margin: 0;
  color: #d1d5db;
}

.next-step-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  width: 100%;
  transition: all 0.2s ease;
}

.next-step-btn:hover {
  background: rgba(245, 158, 11, 0.2);
  border-color: #f59e0b;
}



/* Right Column - Editor Panel */
.editor-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Expanded state when learning column is collapsed */
.editor-column.expanded {
  flex: 1;
}

.editor-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  border-radius: 0.75rem;
  border: 1px solid #333;
  overflow: hidden;
  min-height: 0;
}

.viewer-section {
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  border-radius: 0.75rem 0.75rem 0 0;
  overflow: hidden;
  min-height: 20%;
  /* Remove flex: 1 to allow resizer to control height */
}

.viewer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: #2a2a2a;
  border-bottom: 1px solid #333;
}

.viewer-header h3 {
  margin: 0;
  color: #f9fafb;
  font-size: 1rem;
  font-weight: 600;
}

.run-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.run-button:hover:not(:disabled) {
  background: #059669;
}

.run-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-display {
  background: #991b1b;
  color: #fecaca;
  padding: 0.75rem 1rem;
  border-top: 1px solid #333;
  font-size: 0.875rem;
}

/* Resizer */
.resizer {
  height: 8px;
  background: #333;
  cursor: ns-resize;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 10;
  transition: background-color 0.2s ease;
}

.resizer:hover {
  background: #404040;
}

.resizer.dragging {
  background: #3b82f6;
}

.resizer-line {
  width: 40px;
  height: 2px;
  background: #666;
  border-radius: 1px;
  transition: background-color 0.2s ease;
}

.resizer:hover .resizer-line {
  background: #888;
}

.resizer.dragging .resizer-line {
  background: #ffffff;
}

.code-section {
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  border-radius: 0 0 0.75rem 0.75rem;
  overflow: hidden;
  min-height: 20%;
  /* Remove flex: 1 to allow resizer to control height */
}

.code-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: #2a2a2a;
  border-bottom: 1px solid #333;
}

.code-header h3 {
  margin: 0;
  color: #f9fafb;
  font-size: 1rem;
  font-weight: 600;
}

.reset-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #6b7280;
  color: white;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.reset-button:hover {
  background: #4b5563;
}

/* Mobile-First Responsive Design */

/* Remove problematic desktop breakpoint */

/* Remove problematic tablet breakpoint that breaks desktop */

/* Mobile landscape and small tablets */
@media (max-width: 768px) {
  /* Mobile header is handled by the mobile-header class */
  .lesson-header.desktop {
    display: none !important;
  }

  .lesson-header.mobile {
    display: block !important;
  }

  /* Force mobile header to be horizontal */
  .mobile-header {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: space-between !important;
    flex-wrap: nowrap !important;
    width: 100% !important;
  }

  .mobile-lesson-info {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
  }

  /* MOBILE ONLY - Constrain layout for smaller screens */
  .lesson-content {
    max-height: calc(100vh - 100px) !important;
    height: auto !important;
    min-height: auto !important;
  }

  .learning-column {
    height: auto !important;
    max-height: 45vh !important;
    min-height: 300px !important;
  }

  .editor-column {
    height: auto !important;
    max-height: 45vh !important;
    min-height: 300px !important;
  }

  .lesson-info {
    margin: 0;
    order: -1;
  }

  .lesson-info h1 {
    font-size: 1.5rem;
  }

  .lesson-meta {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .lesson-content {
    margin: 0 1vw 1vh 1vw;
    padding: 0.5rem;
    max-height: calc(100vh - 160px);
  }

  .learning-column {
    max-height: 40vh;
    min-height: 250px;
    height: 40vh;
  }

  /* Collapsed learning column on mobile */
  .learning-column.collapsed {
    height: auto;
    min-height: auto;
    max-height: none;
    margin-bottom: 0.5rem;
  }

  .editor-column {
    min-height: 50vh;
    height: 50vh;
  }

  /* Expanded editor when learning is collapsed on mobile */
  .editor-column.expanded {
    min-height: calc(100vh - 180px);
    height: calc(100vh - 180px);
  }

  .learning-tabs {
    flex-direction: row;
  }

  /* OVERRIDE for smartphones - MUST come after the row rule above */
  @media (max-width: 480px) {
    .learning-tabs {
      flex-direction: column !important;
      position: relative;
    }

    .learning-tab {
      padding: 0.75rem 1rem !important;
      font-size: 0.875rem !important;
      border-bottom: 1px solid #374151;
      border-right: none;
      width: 100% !important;
      text-align: center !important;
    }
  }

  .learning-tab {
    padding: 0.75rem 0.5rem;
    font-size: 0.8rem;
  }

  .steps-container {
    padding: 0.75rem;
  }

  .learning-step {
    margin-bottom: 1rem;
  }

  .step-header {
    padding: 0.75rem;
  }

  .step-content {
    padding: 1rem;
  }

  .code-example pre {
    padding: 0.75rem;
    font-size: 0.8rem;
  }

  .viewer-header,
  .code-header {
    padding: 0.75rem;
  }

  .nav-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
}

/* Mobile portrait */
@media (max-width: 480px) {
  /* Even more compact mobile header */
  .lesson-header.mobile {
    padding: 0.25rem 0.5rem;
  }

  .mobile-header {
    min-height: 28px;
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: space-between !important;
    flex-wrap: nowrap !important;
  }

  .mobile-lesson-info {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
  }

  .mobile-nav-btn {
    width: 28px;
    height: 28px;
  }

  .mobile-title {
    font-size: 0.75rem;
  }

  .mobile-difficulty {
    font-size: 0.6rem;
    padding: 0.1rem 0.3rem;
  }

  /* More content space with ultra-compact header */
  .lesson-content {
    max-height: calc(100vh - 80px);
  }
    gap: 0.25rem;
  }

  .lesson-content {
    margin: 0;
    padding: 0.25rem;
    gap: 0.5rem;
    max-height: calc(100vh - 140px);
  }

  .learning-column {
    max-height: 35vh;
    min-height: 200px;
    height: 35vh;
  }

  /* Collapsed learning column on small mobile */
  .learning-column.collapsed {
    height: auto;
    min-height: auto;
    max-height: none;
    margin-bottom: 0.25rem;
  }

  .editor-column {
    min-height: 55vh;
    height: 55vh;
  }

  /* Expanded editor when learning is collapsed on small mobile */
  .editor-column.expanded {
    min-height: calc(100vh - 160px);
    height: calc(100vh - 160px);
  }

  .learning-tabs {
    flex-direction: column !important; /* Force vertical on smartphones */
    position: relative;
  }

  .learning-tab {
    padding: 0.75rem 1rem !important; /* Better smartphone padding */
    font-size: 0.875rem !important; /* Larger text for smartphones */
    border-bottom: 1px solid #374151;
    border-right: none;
    width: 100% !important; /* Full width on smartphones */
    text-align: center !important; /* Center text */
  }

  .learning-tab.active {
    border-bottom-color: #3b82f6;
  }

  .learning-collapse-btn {
    position: absolute;
    right: 0.25rem;
    top: 0.25rem;
    padding: 0.375rem;
    min-height: 32px;
    min-width: 32px;
  }

  .tab-description {
    padding: 0.75rem;
  }

  .steps-container {
    padding: 0.5rem;
  }

  .learning-step {
    margin-bottom: 0.75rem;
  }

  .step-header {
    padding: 0.5rem;
    gap: 0.5rem;
  }

  .step-info h3 {
    font-size: 0.9rem;
  }

  .step-content {
    padding: 0.75rem;
  }

  .code-example {
    margin-bottom: 1rem;
  }

  .code-example pre {
    padding: 0.5rem;
    font-size: 0.75rem;
    overflow-x: auto;
  }

  .viewer-header,
  .code-header {
    padding: 0.5rem;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .viewer-header h3,
  .code-header h3 {
    font-size: 0.9rem;
  }

  .run-button,
  .reset-button {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
  }

  .nav-button {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
  }

  /* Ensure scrollable content on mobile */
  .steps-container::-webkit-scrollbar {
    width: 4px;
  }

  .docs-container::-webkit-scrollbar {
    width: 4px;
  }
}

/* ========================================
   PROPER MOBILE LAYOUT - 3 SECTIONS VERTICAL
   ======================================== */

/* Mobile phones only - 3-section vertical stack */
@media (max-width: 767px) {
  /* Show mobile header, hide desktop header */
  .lesson-header.desktop {
    display: none !important;
  }

  .lesson-header.mobile {
    display: block !important;
  }

  /* Mobile layout - 3-section vertical stack */
  .lesson-content {
    flex-direction: column !important;
    height: calc(100vh - 80px) !important;
    min-height: calc(100vh - 80px) !important;
    max-height: calc(100vh - 80px) !important;
    margin: 0 1rem 0 1rem !important;
    gap: 0.5rem !important;
    padding: 0.5rem !important;
    overflow: hidden !important;
  }

  /* Learning column - collapsible top section */
  .learning-column {
    height: auto !important;
    min-height: auto !important;
    max-height: 40vh !important;
    flex: none !important;
    margin-bottom: 0 !important;
    overflow: hidden !important;
  }

  /* Learning column when collapsed - minimal height */
  .learning-column.collapsed {
    height: auto !important;
    min-height: auto !important;
    max-height: 60px !important;
  }

  /* Editor column - split into 3D Preview and Code Editor */
  .editor-column {
    flex: 1 !important;
    height: auto !important;
    min-height: 0 !important;
    max-height: none !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
  }

  /* 3D Preview section - middle section */
  .viewer-section {
    height: 40% !important;
    min-height: 150px !important;
    max-height: 40% !important;
    flex: none !important;
  }

  /* Code Editor section - bottom section */
  .code-section {
    flex: 1 !important;
    height: auto !important;
    min-height: 200px !important;
    max-height: none !important;
  }

  /* Mobile editor panel */
  .editor-panel {
    height: 100% !important;
    max-height: 100% !important;
  }

  /* Compact mobile headers */
  .viewer-header,
  .code-header {
    padding: 0.5rem !important;
    font-size: 0.8rem !important;
  }

  /* Mobile button sizes */
  .run-btn,
  .reset-btn {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.75rem !important;
  }
}

/* ========================================
   SMARTPHONE ONLY - VERTICAL TABS
   ======================================== */

/* Smartphone width only - FORCE vertical tabs with BLOCK display */
@media (max-width: 480px) {
  .learning-column .learning-tabs {
    display: block !important;
    width: 100% !important;
  }

  .learning-column .learning-tab {
    display: block !important;
    width: 100% !important;
    margin-bottom: 0.5rem !important;
    font-size: 0.875rem !important;
    padding: 0.75rem 1rem !important;
    text-align: center !important;
    box-sizing: border-box !important;
  }

  .learning-column .learning-tab:last-child {
    margin-bottom: 0 !important;
  }
}

/* Very small screens */
@media (max-width: 360px) {
  .lesson-content {
    padding: 0.125rem;
  }

  .learning-column {
    max-height: 30vh;
    height: 30vh;
  }

  /* Collapsed learning column on very small screens */
  .learning-column.collapsed {
    height: auto;
    min-height: auto;
    max-height: none;
    margin-bottom: 0.125rem;
  }

  .editor-column {
    min-height: 60vh;
    height: 60vh;
  }

  /* Expanded editor when learning is collapsed on very small screens */
  .editor-column.expanded {
    min-height: calc(100vh - 140px);
    height: calc(100vh - 140px);
  }

  .learning-collapse-btn {
    right: 0.125rem;
    top: 0.125rem;
    padding: 0.25rem;
    min-height: 28px;
    min-width: 28px;
  }

  .step-info h3 {
    font-size: 0.85rem;
  }

  .code-example pre {
    font-size: 0.7rem;
  }
}
