.simple-learning-cards {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #1a1a1a;
  border-radius: 0.75rem;
  overflow: hidden;
}

.cards-header {
  padding: 1.5rem;
  border-bottom: 1px solid #333;
  background: #1f2937;
}

.cards-header h3 {
  margin: 0 0 0.5rem 0;
  color: #f9fafb;
  font-size: 1.25rem;
  font-weight: 600;
}

.progress-indicator {
  color: #9ca3af;
  font-size: 0.875rem;
}

.cards-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  scrollbar-width: thin;
  scrollbar-color: #4b5563 #1f2937;
}

.cards-content::-webkit-scrollbar {
  width: 8px;
}

.cards-content::-webkit-scrollbar-track {
  background: #1f2937;
  border-radius: 4px;
}

.cards-content::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

.cards-content::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

.learning-step {
  background: #1f2937;
  border: 1px solid #374151;
  border-radius: 0.75rem;
  margin-bottom: 1rem;
  overflow: hidden;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #2d3748;
  border-bottom: 1px solid #374151;
}

.completion-toggle {
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  transition: color 0.2s ease;
  padding: 0;
  display: flex;
  align-items: center;
}

.completion-toggle:hover {
  color: #9ca3af;
}

.completion-toggle.completed {
  color: #10b981;
}

.step-info {
  flex: 1;
}

.step-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #f9fafb;
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.step-type {
  color: #9ca3af;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.step-content {
  padding: 1.5rem;
}

.step-description {
  color: #e5e7eb;
  line-height: 1.6;
  margin: 0 0 1.5rem 0;
}

.step-objectives {
  margin-bottom: 1.5rem;
}

.step-objectives h4 {
  color: #3b82f6;
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.step-objectives ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #d1d5db;
}

.step-objectives li {
  margin-bottom: 0.25rem;
}

.code-example {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 0.5rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.code-example h4 {
  margin: 0;
  padding: 0.75rem 1rem;
  background: #2a2a2a;
  color: #10b981;
  font-size: 0.875rem;
  font-weight: 600;
  border-bottom: 1px solid #333;
}

.code-example pre {
  margin: 0;
  padding: 1rem;
  background: #0a0a0a;
  overflow-x: auto;
}

.code-example code {
  color: #d1d5db;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.try-code-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-top: 1px solid #333;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  width: 100%;
  transition: background-color 0.2s ease;
}

.try-code-btn:hover {
  background: #2563eb;
}

.explanation {
  background: rgba(59, 130, 246, 0.1);
  border-radius: 0.5rem;
  padding: 1rem;
  border-left: 3px solid #3b82f6;
  margin-bottom: 1.5rem;
}

.explanation h4 {
  margin: 0 0 0.5rem 0;
  color: #3b82f6;
  font-size: 0.875rem;
  font-weight: 600;
}

.explanation p {
  margin: 0;
  color: #d1d5db;
}

.next-step {
  background: rgba(245, 158, 11, 0.1);
  border-radius: 0.5rem;
  padding: 1rem;
  border-left: 3px solid #f59e0b;
  margin-bottom: 1.5rem;
  position: relative;
}

.next-step h4 {
  margin: 0 0 0.5rem 0;
  color: #f59e0b;
  font-size: 0.875rem;
  font-weight: 600;
}

.next-step p {
  margin: 0;
  color: #d1d5db;
  font-style: italic;
}

.next-step::after {
  content: "→";
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #f59e0b;
  font-size: 1.2rem;
  font-weight: bold;
}

.challenges {
  background: rgba(168, 85, 247, 0.1);
  border-radius: 0.5rem;
  padding: 1rem;
  border-left: 3px solid #a855f7;
}

.challenges h4 {
  margin: 0 0 1rem 0;
  color: #a855f7;
  font-size: 0.875rem;
  font-weight: 600;
}

.challenges ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #d1d5db;
}

.challenges li {
  margin-bottom: 0.5rem;
}
