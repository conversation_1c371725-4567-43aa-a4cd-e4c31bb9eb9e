import { useRef, useEffect, useState, useCallback } from 'react'
import * as THREE from 'three'
import './ThreeJSViewer.css'

const ThreeJSViewer = ({ code, codeVersion, onError, onSuccess }) => {
  const mountRef = useRef(null)
  const sceneRef = useRef(null)
  const rendererRef = useRef(null)
  const cameraRef = useRef(null)
  const animationIdRef = useRef(null)
  const canvasRef = useRef(null)
  const [isExecuting, setIsExecuting] = useState(false)
  const [, forceUpdate] = useState({})

  // Initialize Three.js scene (EXACT SAME AS WORKING HTML)
  useEffect(() => {
    if (!mountRef.current) return

    console.log('🎬 Initializing Three.js in React...')

    // Create scene
    const scene = new THREE.Scene()
    scene.background = new THREE.Color(0x1a1a1a)
    sceneRef.current = scene

    // Create camera
    const camera = new THREE.PerspectiveCamera(
      75,
      mountRef.current.clientWidth / mountRef.current.clientHeight,
      0.1,
      1000
    )
    camera.position.set(0, 0, 5)
    cameraRef.current = camera

    // Create renderer
    const renderer = new THREE.WebGLRenderer({ antialias: true })
    renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight)
    rendererRef.current = renderer

    // Add renderer to DOM and ensure it's visible
    renderer.domElement.style.display = 'block'
    renderer.domElement.style.width = '100%'
    renderer.domElement.style.height = '100%'
    renderer.domElement.style.position = 'absolute'
    renderer.domElement.style.top = '0'
    renderer.domElement.style.left = '0'
    renderer.domElement.style.zIndex = '1'

    // Store canvas reference
    canvasRef.current = renderer.domElement

    // Clear any existing content and add renderer
    mountRef.current.innerHTML = ''
    mountRef.current.appendChild(renderer.domElement)

    // Add basic lighting (SAME AS HTML)
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
    scene.add(ambientLight)

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
    directionalLight.position.set(10, 10, 5)
    scene.add(directionalLight)

    // Initial render
    renderer.render(scene, camera)

    console.log('✅ Three.js initialized successfully in React')

    // Handle resize
    const handleResize = () => {
      if (!mountRef.current || !camera || !renderer) return

      const rect = mountRef.current.getBoundingClientRect()
      const width = rect.width
      const height = rect.height

      // Only resize if dimensions are valid
      if (width > 0 && height > 0) {
        camera.aspect = width / height
        camera.updateProjectionMatrix()
        renderer.setSize(width, height)

        // Re-render after resize
        if (scene) {
          renderer.render(scene, camera)
        }
      }
    }

    // Handle custom resize event (for layout changes like collapsing panels)
    const handleCustomResize = () => {
      console.log('🔄 Custom resize event triggered - resizing viewer...')
      // Small delay to ensure layout has settled
      setTimeout(() => {
        if (!mountRef.current || !camera || !renderer) return

        // Force a layout recalculation
        const rect = mountRef.current.getBoundingClientRect()
        const width = rect.width
        const height = rect.height

        console.log(`📏 Resizing viewer to: ${width}x${height}`)

        // Update camera aspect ratio
        camera.aspect = width / height
        camera.updateProjectionMatrix()

        // Update renderer size
        renderer.setSize(width, height)

        // Force re-render
        if (scene && camera && renderer) {
          renderer.render(scene, camera)
        }
      }, 50)
    }

    // Handle lesson change cleanup
    const handleLessonChange = () => {
      console.log('🧹 Clearing canvas for lesson change...')
      if (scene && camera && renderer) {
        // Stop any existing animation
        if (animationIdRef.current) {
          cancelAnimationFrame(animationIdRef.current)
          animationIdRef.current = null
        }

        // Clear the scene completely
        clearScene()

        // Reset scene background
        scene.background = new THREE.Color(0x1a1a1a)

        // Reset camera
        camera.position.set(0, 0, 5)
        camera.lookAt(0, 0, 0)
        camera.rotation.set(0, 0, 0)

        // Clear the canvas
        renderer.clear()
        renderer.render(scene, camera)
      }
    }

    window.addEventListener('resize', handleResize)
    window.addEventListener('resizeViewer', handleCustomResize)
    window.addEventListener('clearCanvasForLessonChange', handleLessonChange)

    return () => {
      console.log('🧹 Cleaning up Three.js...')
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('resizeViewer', handleCustomResize)
      window.removeEventListener('clearCanvasForLessonChange', handleLessonChange)
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current)
      }
      if (mountRef.current && renderer.domElement && mountRef.current.contains(renderer.domElement)) {
        mountRef.current.removeChild(renderer.domElement)
      }
      renderer.dispose()
    }
  }, [])

  // Simple execution: only when codeVersion changes and > 0
  useEffect(() => {
    if (codeVersion === 0 || !code) return

    executeCode()
  }, [codeVersion])

  // Clear scene function to remove all objects
  const clearScene = () => {
    if (!sceneRef.current) return

    console.log('🧹 Clearing scene completely...')

    // Remove ALL objects except the scene itself
    const objectsToRemove = []
    sceneRef.current.traverse((child) => {
      // Remove all object types: Mesh, Group, Points (particles), Line, Sprite, etc.
      if (child !== sceneRef.current && child.type !== 'Scene') {
        objectsToRemove.push(child)
      }
    })

    console.log(`🗑️ Removing ${objectsToRemove.length} objects from scene`)

    objectsToRemove.forEach((object) => {
      // Remove from parent
      if (object.parent) {
        object.parent.remove(object)
      }

      // Dispose of geometry and materials to free memory
      if (object.geometry) {
        console.log(`🗑️ Disposing geometry: ${object.geometry.type}`)
        object.geometry.dispose()
      }

      if (object.material) {
        if (Array.isArray(object.material)) {
          object.material.forEach(material => {
            console.log(`🗑️ Disposing material: ${material.type}`)
            material.dispose()
          })
        } else {
          console.log(`🗑️ Disposing material: ${object.material.type}`)
          object.material.dispose()
        }
      }

      // Dispose of textures if present
      if (object.material && object.material.map) {
        object.material.map.dispose()
      }
    })

    // Clear any remaining children directly from scene
    while (sceneRef.current.children.length > 0) {
      const child = sceneRef.current.children[0]
      sceneRef.current.remove(child)
    }

    console.log('✅ Scene completely cleared')
  }

  // EXACT SAME EXECUTION LOGIC AS WORKING HTML
  const executeCode = () => {
    if (!code || !sceneRef.current || !rendererRef.current || !cameraRef.current) {
      console.log('❌ Missing dependencies for code execution')
      return
    }

    console.log('🚀 Running user code in React...')
    setIsExecuting(true)

    try {
      const scene = sceneRef.current
      const camera = cameraRef.current
      const renderer = rendererRef.current

      // 1. Stop any existing animation (SAME AS HTML)
      console.log('🛑 Stopping any existing animations...')
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current)
        animationIdRef.current = null
        console.log('✅ Previous animation cancelled')
      }

      // 2. Clear previous objects (SAME AS HTML)
      clearScene()

      // 3. Reset scene background to default
      scene.background = new THREE.Color(0x1a1a1a) // Dark background

      // 4. Reset camera (SAME AS HTML)
      camera.position.set(0, 0, 5)
      camera.lookAt(0, 0, 0)
      camera.rotation.set(0, 0, 0)

      // 5. Clear and reset renderer
      renderer.clear()
      renderer.clearDepth()
      renderer.resetState()

      // 6. Create helper functions (SAME AS HTML)
      const createMesh = (geometry, material) => {
        const mesh = new THREE.Mesh(geometry, material)
        mesh.userData.userCreated = true
        return mesh
      }

      const animate = (callback) => {
        // Cancel any existing animation
        if (animationIdRef.current) {
          cancelAnimationFrame(animationIdRef.current)
          animationIdRef.current = null
        }

        console.log('🎬 Starting animation loop...')
        let frameCount = 0

        const loop = () => {
          animationIdRef.current = requestAnimationFrame(loop)

          try {
            callback()

            // Force render with explicit parameters
            renderer.render(scene, camera)

            // Force canvas update (React fix)
            if (renderer.domElement) {
              renderer.domElement.style.transform = 'translateZ(0)' // Force GPU layer
            }

            // Debug: log every 60 frames to verify it's running
            frameCount++
            if (frameCount % 60 === 0) {
              console.log(`🎬 Animation running: frame ${frameCount}`)
              console.log(`📐 Camera position:`, camera.position.x, camera.position.y, camera.position.z)
              console.log(`🎯 Scene children:`, scene.children.length)
            }
          } catch (error) {
            console.error('❌ Animation callback error:', error)
            // Don't stop the loop on errors
          }
        }

        // Start the loop immediately
        loop()
      }

      // 7. Execute user code (SAME AS HTML)
      console.log('📝 Executing user code...')
      const userFunction = new Function(
        'THREE', 'scene', 'camera', 'renderer', 'createMesh', 'animate',
        code
      )
      userFunction(THREE, scene, camera, renderer, createMesh, animate)

      // 8. Render once and force canvas update
      renderer.render(scene, camera)

      // Force canvas to be visible and properly sized
      if (renderer.domElement && mountRef.current) {
        const rect = mountRef.current.getBoundingClientRect()
        renderer.setSize(rect.width, rect.height)
        camera.aspect = rect.width / rect.height
        camera.updateProjectionMatrix()
        renderer.render(scene, camera) // Render again after resize

        console.log(`📏 Canvas resized to: ${rect.width}x${rect.height}`)
      }

      console.log('✅ Code execution completed successfully')
      setIsExecuting(false)
      if (onSuccess) onSuccess()

    } catch (error) {
      console.error('❌ Code execution error:', error)
      setIsExecuting(false)
      if (onError) onError(error.message)
    }
  }



  return (
    <div className="threejs-viewer">
      <div
        ref={mountRef}
        className="viewer-canvas"
        style={{
          width: '100%',
          height: '100%',
          position: 'relative'
        }}
      >
        {isExecuting && (
          <div className="viewer-loading">
            <div className="loading-spinner"></div>
            <span>Running code...</span>
          </div>
        )}
      </div>
    </div>
  )
}

export default ThreeJSViewer
