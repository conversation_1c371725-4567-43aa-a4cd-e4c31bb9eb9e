.doc-tooltip-container {
  position: relative;
  display: inline-block;
}

.doc-tooltip-trigger {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  cursor: help;
  color: #3b82f6;
  text-decoration: underline;
  text-decoration-style: dotted;
  text-underline-offset: 2px;
}

.doc-tooltip-trigger:hover {
  color: #60a5fa;
}

.doc-tooltip-icon {
  opacity: 0.7;
  transition: opacity 0.2s;
}

.doc-tooltip-trigger:hover .doc-tooltip-icon {
  opacity: 1;
}

.doc-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 0.5rem;
  z-index: 1000;
  animation: tooltipFadeIn 0.2s ease-out;
}

.doc-tooltip-content {
  background: #2a2a2a;
  border: 1px solid #404040;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  max-width: 300px;
  min-width: 200px;
  color: #ffffff;
  position: relative;
}

.doc-tooltip-content::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: #2a2a2a;
}

.doc-tooltip-content h4 {
  margin: 0 0 0.5rem 0;
  color: #3b82f6;
  font-size: 0.875rem;
  font-weight: 600;
}

.doc-tooltip-content p {
  margin: 0 0 0.75rem 0;
  font-size: 0.75rem;
  line-height: 1.4;
  color: #d1d5db;
}

.doc-tooltip-link {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  color: #60a5fa;
  text-decoration: none;
  font-size: 0.75rem;
  font-weight: 500;
  transition: color 0.2s;
}

.doc-tooltip-link:hover {
  color: #93c5fd;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Responsive positioning */
@media (max-width: 768px) {
  .doc-tooltip {
    position: fixed;
    bottom: auto;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin: 0;
  }
  
  .doc-tooltip-content {
    max-width: 280px;
  }
  
  .doc-tooltip-content::after {
    display: none;
  }
}
