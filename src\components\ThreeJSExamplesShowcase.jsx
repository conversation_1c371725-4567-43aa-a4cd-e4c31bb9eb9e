import React, { useRef, useEffect } from 'react'
import * as THREE from 'three'
import './ThreeJSExamplesShowcase.css'

const ThreeJSExamplesShowcase = () => {
  return (
    <section className="examples-showcase">
      <div className="container">
        <h2>What You'll Create</h2>
        <p>Explore the possibilities with Three.js - from basic shapes to complex materials and animations</p>
        <div className="examples-grid">
          <ExampleCard1 />
          <ExampleCard2 />
          <ExampleCard3 />
        </div>
      </div>
    </section>
  );
};

// Example 1: Particle Galaxy
const ExampleCard1 = () => {
  const mountRef = useRef(null);
  const animationIdRef = useRef(null);

  useEffect(() => {
    if (!mountRef.current) return;

    // Basic Three.js setup
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x0a0a0a);
    
    const camera = new THREE.PerspectiveCamera(75, mountRef.current.clientWidth / mountRef.current.clientHeight, 0.1, 1000);
    camera.position.z = 8;
    
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);

    // Add renderer to DOM and ensure it's visible (EXACT SAME AS WORKING ThreeJSViewer)
    renderer.domElement.style.display = 'block';
    renderer.domElement.style.width = '100%';
    renderer.domElement.style.height = '100%';
    renderer.domElement.style.position = 'absolute';
    renderer.domElement.style.top = '0';
    renderer.domElement.style.left = '0';
    renderer.domElement.style.zIndex = '1';

    // Clear any existing content and add renderer
    mountRef.current.innerHTML = '';
    mountRef.current.appendChild(renderer.domElement);

    // Create particle system
    const particleCount = 1000;
    const particles = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      const radius = Math.random() * 5;
      const angle = Math.random() * Math.PI * 2;
      
      positions[i3] = Math.cos(angle) * radius;
      positions[i3 + 1] = (Math.random() - 0.5) * 2;
      positions[i3 + 2] = Math.sin(angle) * radius;
      
      colors[i3] = 0.5 + Math.random() * 0.5;
      colors[i3 + 1] = 0.3 + Math.random() * 0.7;
      colors[i3 + 2] = 1;
    }

    particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));

    const material = new THREE.PointsMaterial({
      size: 0.05,
      vertexColors: true,
      transparent: true,
      opacity: 0.8
    });

    const particleSystem = new THREE.Points(particles, material);
    scene.add(particleSystem);

    // Animation function - EXACT SAME AS WORKING ThreeJSViewer
    const animate = (callback) => {
      // Cancel any existing animation
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
        animationIdRef.current = null;
      }

      console.log('🎬 Starting animation loop for Particle Galaxy...');
      let frameCount = 0;

      const loop = () => {
        animationIdRef.current = requestAnimationFrame(loop);

        try {
          callback();

          // Force render with explicit parameters
          renderer.render(scene, camera);

          // Force canvas update (React fix)
          if (renderer.domElement) {
            renderer.domElement.style.transform = 'translateZ(0)'; // Force GPU layer
          }

          // Debug: log every 60 frames to verify it's running
          frameCount++;
          if (frameCount % 60 === 0) {
            console.log(`🎬 Particle Galaxy animation running: frame ${frameCount}`);
          }
        } catch (error) {
          console.error('❌ Animation callback error:', error);
          // Don't stop the loop on errors
        }
      };

      // Start the loop immediately
      loop();
    };

    // Start animation with callback
    animate(() => {
      particleSystem.rotation.y += 0.002;
    });

    return () => {
      if (animationIdRef.current) cancelAnimationFrame(animationIdRef.current);
      renderer.dispose();
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
    };
  }, []);

  return (
    <div className="example-card">
      <div className="example-preview" ref={mountRef}></div>
      <div className="example-content">
        <h3>Particle Galaxy</h3>
        <p>Mesmerizing swirling particle systems with dynamic colors</p>
      </div>
    </div>
  );
};

// Example 2: Morphing Geometry
const ExampleCard2 = () => {
  const mountRef = useRef(null);
  const animationIdRef = useRef(null);

  useEffect(() => {
    if (!mountRef.current) return;

    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x0a0a0a);
    
    const camera = new THREE.PerspectiveCamera(75, mountRef.current.clientWidth / mountRef.current.clientHeight, 0.1, 1000);
    camera.position.z = 8;
    
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);

    // Add renderer to DOM and ensure it's visible (EXACT SAME AS WORKING ThreeJSViewer)
    renderer.domElement.style.display = 'block';
    renderer.domElement.style.width = '100%';
    renderer.domElement.style.height = '100%';
    renderer.domElement.style.position = 'absolute';
    renderer.domElement.style.top = '0';
    renderer.domElement.style.left = '0';
    renderer.domElement.style.zIndex = '1';

    // Clear any existing content and add renderer
    mountRef.current.innerHTML = '';
    mountRef.current.appendChild(renderer.domElement);

    // Create morphing objects
    const objects = [];
    for (let i = 0; i < 3; i++) {
      const geometry = new THREE.BoxGeometry(1, 1, 1);
      const material = new THREE.MeshBasicMaterial({ 
        color: new THREE.Color().setHSL(i * 0.3, 0.8, 0.6),
        wireframe: true
      });
      const cube = new THREE.Mesh(geometry, material);
      cube.position.x = (i - 1) * 3;
      objects.push(cube);
      scene.add(cube);
    }

    // Animation function - EXACT SAME AS WORKING ThreeJSViewer
    const animate = (callback) => {
      // Cancel any existing animation
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
        animationIdRef.current = null;
      }

      console.log('🎬 Starting animation loop for Morphing Geometry...');
      let frameCount = 0;

      const loop = () => {
        animationIdRef.current = requestAnimationFrame(loop);

        try {
          callback();

          // Force render with explicit parameters
          renderer.render(scene, camera);

          // Force canvas update (React fix)
          if (renderer.domElement) {
            renderer.domElement.style.transform = 'translateZ(0)'; // Force GPU layer
          }

          // Debug: log every 60 frames to verify it's running
          frameCount++;
          if (frameCount % 60 === 0) {
            console.log(`🎬 Morphing Geometry animation running: frame ${frameCount}`);
          }
        } catch (error) {
          console.error('❌ Animation callback error:', error);
          // Don't stop the loop on errors
        }
      };

      // Start the loop immediately
      loop();
    };

    // Start animation with callback
    animate(() => {
      const time = Date.now() * 0.001;

      objects.forEach((obj, index) => {
        obj.rotation.x = time + index;
        obj.rotation.y = time * 0.7 + index;
        obj.position.y = Math.sin(time * 2 + index) * 1;
        obj.scale.setScalar(1 + Math.sin(time * 3 + index) * 0.3);
      });
    });

    return () => {
      if (animationIdRef.current) cancelAnimationFrame(animationIdRef.current);
      renderer.dispose();
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
    };
  }, []);

  return (
    <div className="example-card">
      <div className="example-preview" ref={mountRef}></div>
      <div className="example-content">
        <h3>Morphing Geometry</h3>
        <p>Shape-shifting 3D objects with smooth transitions</p>
      </div>
    </div>
  );
};

// Example 3: Liquid Metal
const ExampleCard3 = () => {
  const mountRef = useRef(null);
  const animationIdRef = useRef(null);

  useEffect(() => {
    if (!mountRef.current) return;

    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x0a0a0a);
    
    const camera = new THREE.PerspectiveCamera(75, mountRef.current.clientWidth / mountRef.current.clientHeight, 0.1, 1000);
    camera.position.set(0, 2, 6);
    
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);

    // Add renderer to DOM and ensure it's visible (EXACT SAME AS WORKING ThreeJSViewer)
    renderer.domElement.style.display = 'block';
    renderer.domElement.style.width = '100%';
    renderer.domElement.style.height = '100%';
    renderer.domElement.style.position = 'absolute';
    renderer.domElement.style.top = '0';
    renderer.domElement.style.left = '0';
    renderer.domElement.style.zIndex = '1';

    // Clear any existing content and add renderer
    mountRef.current.innerHTML = '';
    mountRef.current.appendChild(renderer.domElement);

    // Create liquid surface
    const geometry = new THREE.PlaneGeometry(4, 4, 16, 16);
    const material = new THREE.MeshBasicMaterial({ 
      color: 0x2c3e50,
      wireframe: true
    });
    const plane = new THREE.Mesh(geometry, material);
    plane.rotation.x = -Math.PI / 2;
    scene.add(plane);

    // Create floating spheres
    const spheres = [];
    for (let i = 0; i < 4; i++) {
      const sphere = new THREE.Mesh(
        new THREE.SphereGeometry(0.2, 8, 8),
        new THREE.MeshBasicMaterial({ 
          color: new THREE.Color().setHSL(i * 0.25, 0.8, 0.6)
        })
      );
      sphere.position.set(
        (Math.random() - 0.5) * 3,
        1 + Math.random(),
        (Math.random() - 0.5) * 3
      );
      spheres.push(sphere);
      scene.add(sphere);
    }

    // Animation function - EXACT SAME AS WORKING ThreeJSViewer
    const animate = (callback) => {
      // Cancel any existing animation
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
        animationIdRef.current = null;
      }

      console.log('🎬 Starting animation loop for Liquid Metal...');
      let frameCount = 0;

      const loop = () => {
        animationIdRef.current = requestAnimationFrame(loop);

        try {
          callback();

          // Force render with explicit parameters
          renderer.render(scene, camera);

          // Force canvas update (React fix)
          if (renderer.domElement) {
            renderer.domElement.style.transform = 'translateZ(0)'; // Force GPU layer
          }

          // Debug: log every 60 frames to verify it's running
          frameCount++;
          if (frameCount % 60 === 0) {
            console.log(`🎬 Liquid Metal animation running: frame ${frameCount}`);
          }
        } catch (error) {
          console.error('❌ Animation callback error:', error);
          // Don't stop the loop on errors
        }
      };

      // Start the loop immediately
      loop();
    };

    // Start animation with callback
    animate(() => {
      const time = Date.now() * 0.001;

      // Animate surface
      const positions = plane.geometry.attributes.position.array;
      for (let i = 0; i < positions.length; i += 3) {
        const x = positions[i];
        const z = positions[i + 2];
        positions[i + 1] = Math.sin(x + time) * 0.2 + Math.cos(z + time * 1.5) * 0.1;
      }
      plane.geometry.attributes.position.needsUpdate = true;

      // Animate spheres
      spheres.forEach((sphere, index) => {
        sphere.position.y = 1 + Math.sin(time * 2 + index) * 0.5;
        sphere.rotation.x += 0.01;
        sphere.rotation.y += 0.02;
      });
    });

    return () => {
      if (animationIdRef.current) cancelAnimationFrame(animationIdRef.current);
      renderer.dispose();
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
    };
  }, []);

  return (
    <div className="example-card">
      <div className="example-preview" ref={mountRef}></div>
      <div className="example-content">
        <h3>Liquid Metal</h3>
        <p>Reflective animated surface with dynamic deformations</p>
      </div>
    </div>
  );
};

export default ThreeJSExamplesShowcase;
