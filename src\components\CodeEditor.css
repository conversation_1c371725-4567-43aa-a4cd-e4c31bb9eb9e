.code-editor {
  flex: 1;
  background: #1e1e1e;
  min-height: 200px;
  height: 100%;
  width: 100%;
  border-radius: 0 0 0.5rem 0.5rem;
  position: relative;
}

/* Monaco Editor overrides */
.monaco-editor {
  background: #1e1e1e !important;
}

.monaco-editor .margin {
  background: #1e1e1e !important;
}

.monaco-editor .monaco-editor-background {
  background: #1e1e1e !important;
}

.monaco-editor .current-line {
  background: rgba(255, 255, 255, 0.05) !important;
}

.monaco-editor .line-numbers {
  color: #6b7280 !important;
  padding-right: 12px !important;
}

.monaco-editor .cursor {
  color: #ffffff !important;
}

/* Remove horizontal scrolling completely */
.monaco-editor .monaco-scrollable-element.editor-scrollable.vs-dark {
  overflow-x: hidden !important;
  overflow-y: auto !important;
}

/* Hide duplicate scrollbars - keep only the main editor scrollbar */
.monaco-editor .monaco-scrollable-element.editor-scrollable .scrollbar {
  display: none !important;
}

/* Enhanced Scrollbar styling - Always visible when needed */
.monaco-editor .monaco-scrollable-element > .scrollbar {
  background: rgba(0, 0, 0, 0.1) !important;
  opacity: 1 !important;
}

.monaco-editor .monaco-scrollable-element > .scrollbar > .slider {
  background: rgba(255, 255, 255, 0.4) !important;
  border-radius: 6px !important;
  opacity: 1 !important;
}

.monaco-editor .monaco-scrollable-element > .scrollbar > .slider:hover {
  background: rgba(255, 255, 255, 0.6) !important;
}

.monaco-editor .monaco-scrollable-element > .scrollbar > .slider.active {
  background: rgba(255, 255, 255, 0.7) !important;
}

/* Force scrollbars to always be the correct size */
.monaco-editor .monaco-scrollable-element > .scrollbar.vertical {
  width: 12px !important;
  display: block !important;
}

.monaco-editor .monaco-scrollable-element > .scrollbar.horizontal {
  height: 12px !important;
  display: block !important;
}

/* Ensure scrollable area is properly configured */
.monaco-editor .monaco-scrollable-element {
  overflow: auto !important;
}

/* Make sure the editor content area allows scrolling */
.monaco-editor .lines-content {
  overflow: visible !important;
}

/* Suggestion widget styling */
.monaco-editor .suggest-widget {
  background: #2a2a2a !important;
  border: 1px solid #404040 !important;
  border-radius: 4px !important;
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row {
  background: transparent !important;
  color: #ffffff !important;
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row:hover {
  background: #3a3a3a !important;
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row.focused {
  background: #3b82f6 !important;
}

/* Parameter hints styling */
.monaco-editor .parameter-hints-widget {
  background: #2a2a2a !important;
  border: 1px solid #404040 !important;
  border-radius: 4px !important;
  color: #ffffff !important;
}

/* Hover widget styling */
.monaco-editor .monaco-hover {
  background: #2a2a2a !important;
  border: 1px solid #404040 !important;
  border-radius: 4px !important;
  color: #ffffff !important;
}

/* Find widget styling */
.monaco-editor .find-widget {
  background: #2a2a2a !important;
  border: 1px solid #404040 !important;
  border-radius: 4px !important;
}

.monaco-editor .find-widget input {
  background: #1e1e1e !important;
  color: #ffffff !important;
  border: 1px solid #404040 !important;
}

.monaco-editor .find-widget .button {
  background: #3a3a3a !important;
  color: #ffffff !important;
}

.monaco-editor .find-widget .button:hover {
  background: #4a4a4a !important;
}

/* Context menu styling */
.monaco-menu {
  background: #2a2a2a !important;
  border: 1px solid #404040 !important;
  border-radius: 4px !important;
  color: #ffffff !important;
}

.monaco-menu .monaco-action-bar .action-item .action-label {
  color: #ffffff !important;
}

.monaco-menu .monaco-action-bar .action-item .action-label:hover {
  background: #3a3a3a !important;
}

/* Error and warning decorations */
.monaco-editor .squiggly-error {
  border-bottom: 2px solid #ef4444 !important;
}

.monaco-editor .squiggly-warning {
  border-bottom: 2px solid #f59e0b !important;
}

.monaco-editor .squiggly-info {
  border-bottom: 2px solid #3b82f6 !important;
}

/* Bracket matching */
.monaco-editor .bracket-match {
  background: rgba(59, 130, 246, 0.3) !important;
  border: 1px solid #3b82f6 !important;
}

/* Selection highlighting */
.monaco-editor .selected-text {
  background: rgba(59, 130, 246, 0.3) !important;
}

.monaco-editor .current-line-exact {
  background: rgba(255, 255, 255, 0.05) !important;
}

/* Indentation guides */
.monaco-editor .lines-content .cigr {
  box-shadow: 1px 0 0 0 rgba(255, 255, 255, 0.1) inset !important;
}

.monaco-editor .lines-content .cigra {
  box-shadow: 1px 0 0 0 rgba(59, 130, 246, 0.4) inset !important;
}

/* Mobile-First Responsive Design */

/* Tablets */
@media (max-width: 1024px) {
  .code-editor {
    min-height: 180px;
  }

  .monaco-editor .monaco-scrollable-element > .scrollbar.vertical {
    width: 10px !important;
  }

  .monaco-editor .monaco-scrollable-element > .scrollbar.horizontal {
    height: 10px !important;
  }
}

/* Mobile landscape and small tablets */
@media (max-width: 768px) {
  .code-editor {
    min-height: 150px;
  }

  /* Larger scrollbars for touch */
  .monaco-editor .monaco-scrollable-element > .scrollbar.vertical {
    width: 14px !important;
  }

  .monaco-editor .monaco-scrollable-element > .scrollbar.horizontal {
    height: 14px !important;
  }

  .monaco-editor .monaco-scrollable-element > .scrollbar > .slider {
    background: rgba(255, 255, 255, 0.6) !important;
  }

  /* Larger line numbers for readability */
  .monaco-editor .line-numbers {
    padding-right: 8px !important;
  }

  /* Adjust suggestion widget for mobile */
  .monaco-editor .suggest-widget {
    max-width: 90vw !important;
    max-height: 40vh !important;
  }

  /* Adjust hover widget for mobile */
  .monaco-editor .monaco-hover {
    max-width: 90vw !important;
  }

  /* Adjust find widget for mobile */
  .monaco-editor .find-widget {
    max-width: 90vw !important;
  }
}

/* Mobile portrait */
@media (max-width: 480px) {
  .code-editor {
    min-height: 120px;
  }

  /* Even larger scrollbars for small screens */
  .monaco-editor .monaco-scrollable-element > .scrollbar.vertical {
    width: 16px !important;
  }

  .monaco-editor .monaco-scrollable-element > .scrollbar.horizontal {
    height: 16px !important;
  }

  .monaco-editor .monaco-scrollable-element > .scrollbar > .slider {
    background: rgba(255, 255, 255, 0.7) !important;
    border-radius: 8px !important;
  }

  /* Adjust line numbers */
  .monaco-editor .line-numbers {
    padding-right: 6px !important;
    font-size: 0.8rem !important;
  }

  /* Mobile-friendly widgets */
  .monaco-editor .suggest-widget {
    max-width: 95vw !important;
    max-height: 35vh !important;
    font-size: 0.9rem !important;
  }

  .monaco-editor .monaco-hover {
    max-width: 95vw !important;
    font-size: 0.9rem !important;
  }

  .monaco-editor .find-widget {
    max-width: 95vw !important;
    font-size: 0.9rem !important;
  }

  .monaco-editor .find-widget input {
    font-size: 0.9rem !important;
  }

  /* Context menu adjustments */
  .monaco-menu {
    max-width: 90vw !important;
    font-size: 0.9rem !important;
  }
}

/* Very small screens */
@media (max-width: 360px) {
  .code-editor {
    min-height: 100px;
  }

  .monaco-editor .line-numbers {
    padding-right: 4px !important;
    font-size: 0.75rem !important;
  }

  .monaco-editor .suggest-widget {
    max-width: 98vw !important;
    max-height: 30vh !important;
    font-size: 0.8rem !important;
  }

  .monaco-editor .monaco-hover {
    max-width: 98vw !important;
    font-size: 0.8rem !important;
  }

  .monaco-editor .find-widget {
    max-width: 98vw !important;
    font-size: 0.8rem !important;
  }

  .monaco-menu {
    max-width: 95vw !important;
    font-size: 0.8rem !important;
  }
}

/* Touch-friendly adjustments */
@media (max-width: 768px) and (pointer: coarse) {
  /* Larger touch targets for scrollbars */
  .monaco-editor .monaco-scrollable-element > .scrollbar.vertical {
    width: 18px !important;
  }

  .monaco-editor .monaco-scrollable-element > .scrollbar.horizontal {
    height: 18px !important;
  }

  .monaco-editor .monaco-scrollable-element > .scrollbar > .slider {
    background: rgba(255, 255, 255, 0.8) !important;
    min-height: 30px !important;
    min-width: 30px !important;
  }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .code-editor {
    min-height: 100px;
  }

  .monaco-editor .suggest-widget {
    max-height: 50vh !important;
  }
}
