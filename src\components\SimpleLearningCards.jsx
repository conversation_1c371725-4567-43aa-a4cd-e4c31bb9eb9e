import { useState, useEffect } from 'react'
import { 
  CheckCircle, 
  Circle, 
  Code, 
  Lightbulb, 
  Target,
  Play
} from 'lucide-react'
import './SimpleLearningCards.css'

const SimpleLearningCards = ({ lesson, onCardComplete, onCodeInsert }) => {
  const [completedCards, setCompletedCards] = useState(new Set())

  // Load progress from localStorage
  useEffect(() => {
    const savedProgress = localStorage.getItem(`lesson-${lesson.id}-progress`)
    if (savedProgress) {
      try {
        const progress = JSON.parse(savedProgress)
        setCompletedCards(new Set(progress.completedCards || []))
      } catch (error) {
        console.error('Error loading progress:', error)
      }
    }
  }, [lesson.id])

  // Save progress to localStorage
  const saveProgress = (newCompletedCards) => {
    const progress = {
      completedCards: Array.from(newCompletedCards),
      lastUpdated: Date.now()
    }
    localStorage.setItem(`lesson-${lesson.id}-progress`, JSON.stringify(progress))
  }

  const handleCardComplete = (cardIndex) => {
    const newCompleted = new Set(completedCards)
    if (newCompleted.has(cardIndex)) {
      newCompleted.delete(cardIndex)
    } else {
      newCompleted.add(cardIndex)
    }
    
    setCompletedCards(newCompleted)
    saveProgress(newCompleted)
    
    if (onCardComplete) {
      onCardComplete(cardIndex, newCompleted.has(cardIndex))
    }
  }

  const handleCodeInsert = (code) => {
    if (onCodeInsert) {
      onCodeInsert(code)
    }
  }

  const getIconForType = (type) => {
    switch (type) {
      case 'overview':
        return <Target size={16} />
      case 'hands-on':
        return <Code size={16} />
      case 'challenge':
        return <Lightbulb size={16} />
      default:
        return <Circle size={16} />
    }
  }

  const cards = lesson.learningPath || []
  const completedCount = completedCards.size
  const totalCards = cards.filter(card => card.isRequired !== false).length

  return (
    <div className="simple-learning-cards">
      <div className="cards-header">
        <h3>Learning Path</h3>
        <div className="progress-indicator">
          <span>{completedCount} / {totalCards} completed</span>
        </div>
      </div>

      <div className="cards-content">
        {cards.map((card, index) => (
          <div key={card.id} className="learning-step">
            <div className="step-header">
              <button
                className={`completion-toggle ${completedCards.has(index) ? 'completed' : ''}`}
                onClick={() => handleCardComplete(index)}
                title={completedCards.has(index) ? 'Mark as incomplete' : 'Mark as complete'}
              >
                {completedCards.has(index) ? (
                  <CheckCircle size={20} />
                ) : (
                  <Circle size={20} />
                )}
              </button>
              
              <div className="step-info">
                <div className="step-title">
                  {getIconForType(card.type)}
                  <span>{card.title}</span>
                </div>
                <div className="step-type">{card.type}</div>
              </div>
            </div>

            <div className="step-content">
              <p className="step-description">{card.content}</p>
              
              {card.objectives && (
                <div className="step-objectives">
                  <h4>Learning Objectives:</h4>
                  <ul>
                    {card.objectives.map((objective, idx) => (
                      <li key={idx}>{objective}</li>
                    ))}
                  </ul>
                </div>
              )}

              {card.codeExample && (
                <div className="code-example">
                  <h4>📝 Code Example:</h4>
                  <pre><code>{card.codeExample}</code></pre>
                  <button 
                    className="try-code-btn"
                    onClick={() => handleCodeInsert(card.codeExample)}
                  >
                    <Play size={16} />
                    Try this code
                  </button>
                </div>
              )}

              {card.explanation && (
                <div className="explanation">
                  <h4>💡 Explanation:</h4>
                  <p>{card.explanation}</p>
                </div>
              )}

              {card.nextStep && (
                <div className="next-step">
                  <h4>➡️ Coming up next:</h4>
                  <p>{card.nextStep}</p>
                </div>
              )}

              {card.challenges && (
                <div className="challenges">
                  <h4>🧪 Try these experiments:</h4>
                  <ul>
                    {card.challenges.map((challenge, idx) => (
                      <li key={idx}>{challenge}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default SimpleLearningCards
