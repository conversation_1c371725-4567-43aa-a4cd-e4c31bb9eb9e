.enhanced-documentation {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
  padding: 8px;
}

.enhanced-documentation::-webkit-scrollbar {
  width: 4px;
}

.enhanced-documentation::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.enhanced-documentation::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 2px;
}

.docs-header {
  margin-bottom: 8px;
}

.docs-header h3 {
  margin: 0 0 6px 0;
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
}

.search-box {
  display: flex;
  align-items: center;
  background: #2a2a2a;
  border: 1px solid #333;
  border-radius: 4px;
  padding: 6px 8px;
  gap: 6px;
}

.search-box:focus-within {
  border-color: #3b82f6;
}

.search-box svg {
  color: #9ca3af;
  width: 14px;
  height: 14px;
}

.search-box input {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 12px;
  outline: none;
  flex: 1;
}

.search-box input::placeholder {
  color: #6b7280;
}

.docs-cards {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 0 4px 0;
  margin-top: 8px;
}

.category-header:first-child {
  margin-top: 0;
}

.category-icon {
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
}

.category-title {
  margin: 0;
  font-size: 10px;
  font-weight: 600;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.doc-card {
  background: #2a2a2a;
  border: 1px solid #333;
  border-radius: 3px;
  text-decoration: none;
  color: inherit;
  transition: border-color 0.2s ease;
  display: block;
  padding: 6px 8px;
}

.doc-card:hover {
  border-color: #3b82f6;
}

.doc-card-header {
  display: flex;
  align-items: center;
  gap: 6px;
}

.doc-card-icon {
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  flex-shrink: 0;
}

.doc-card-content {
  flex: 1;
  min-width: 0;
}

.doc-card-title {
  margin: 0;
  font-size: 12px;
  font-weight: 500;
  color: #ffffff;
  line-height: 1.2;
}

.doc-card-category {
  font-size: 9px;
  color: #9ca3af;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  margin-top: 1px;
}

.doc-card-external {
  color: #6b7280;
  width: 12px;
  height: 12px;
  flex-shrink: 0;
}

.doc-card:hover .doc-card-external {
  color: #3b82f6;
}

.doc-card-description {
  font-size: 10px;
  color: #9ca3af;
  line-height: 1.3;
  margin-top: 3px;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
  text-align: center;
  color: #9ca3af;
  gap: 4px;
}

.no-results p {
  margin: 0;
  font-size: 12px;
}

.no-results-hint {
  font-size: 10px;
  color: #6b7280;
}
