---
type: "always_apply"
---

# Development Guidelines
 
This document outlines the development guidelines, workflows, and architectural decisions for this project.
 
## Planning
 
### Task Planning Process
- [ ] Read and understand the task requirements
- [ ] Gather necessary information about the codebase
- [ ] Create detailed implementation plan with specific steps
- [ ] Break down complex tasks into smaller, manageable chunks
- [ ] Identify potential risks and dependencies
- [ ] Document progress with checkboxes for tracking
 
### Implementation Planning
- Create `.ai/task.md` file for each new task with:
  - Task background and requirements
  - Detailed implementation plan
  - Progress tracking with checkboxes
  - Real-time updates throughout execution
 
## General Rules
 
### Code Quality
- All variables must be type hinted
- All methods must have explicit return types
- Comment non-obvious code with inline explanations
- Never hallucinate libraries or functions - verify they exist
- Ask questions when uncertain about requirements
 
### Development Practices
- Use appropriate package managers for dependency management
- Never manually edit package configuration files
- Verify file paths exist before referencing them
 
### Testing Standards
- Write unit tests for new features
- Test expected cases, edge cases, and failure scenarios
- Update existing tests when logic changesw
## MCP Servers
- Use context7 often, read documentation for frameworks and packages
- Utilise MCP servers often where appropriate
- Use memory often so you are constantly learning
- Use the browser when appropriate
 
## Notes
- Review and refine guidelines regularly