/* AI Lesson Generator Styles */
.ai-lesson-generator {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #1a1a1a;
  color: #ffffff;
  overflow-y: auto;
}

.generator-header {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.header-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
  color: #ffffff;
}

.generator-header h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
}

.generator-header p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

.prompt-section {
  padding: 0 20px;
  margin-bottom: 20px;
}

.prompt-input-container {
  margin-bottom: 16px;
}

.prompt-input {
  width: 100%;
  min-height: 80px;
  padding: 12px;
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 6px;
  color: #ffffff;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  margin-bottom: 12px;
}

.prompt-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.prompt-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.prompt-actions {
  display: flex;
  gap: 12px;
}

.generate-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.generate-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.generate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.reset-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #444;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.reset-btn:hover {
  background: #555;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-message {
  padding: 12px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 6px;
  margin-bottom: 16px;
}

.error-message p {
  margin: 0;
  color: #ef4444;
  font-size: 14px;
}

.example-prompts {
  margin-top: 20px;
}

.example-prompts h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #cccccc;
}

.prompt-examples {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.example-prompt {
  padding: 10px 12px;
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 4px;
  color: #cccccc;
  font-size: 12px;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
}

.example-prompt:hover:not(:disabled) {
  background: #333;
  border-color: #555;
  color: #ffffff;
}

.example-prompt:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.generated-lesson {
  padding: 0 20px 20px;
  border-top: 1px solid #333;
  margin-top: 20px;
}

.lesson-header {
  padding: 20px 0;
  border-bottom: 1px solid #333;
  margin-bottom: 20px;
}

.lesson-header h2 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #ffffff;
}

.lesson-header p {
  margin: 0;
  font-size: 14px;
  color: #cccccc;
  line-height: 1.5;
}

.lesson-concepts {
  margin-bottom: 24px;
}

.lesson-concepts h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #ffffff;
}

.concepts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.concept-card {
  padding: 12px;
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 6px;
}

.concept-card h4 {
  margin: 0 0 6px 0;
  font-size: 13px;
  color: #ffffff;
}

.concept-card p {
  margin: 0;
  font-size: 11px;
  color: #cccccc;
  line-height: 1.4;
}

.lesson-steps {
  margin-bottom: 20px;
}

.lesson-steps h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #ffffff;
}

.lesson-step {
  margin-bottom: 20px;
  padding: 16px;
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 6px;
}

.lesson-step h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #ffffff;
}

.step-code {
  margin-top: 12px;
}

.code-explanation {
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(102, 126, 234, 0.1);
  border-left: 3px solid #667eea;
  font-size: 12px;
  color: #cccccc;
}

.code-explanation strong {
  color: #ffffff;
}

/* Main lesson code section */
.lesson-main-code {
  background: #2a2a2a;
  border: 1px solid #404040;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.code-header h3 {
  margin: 0;
  color: #ffffff;
  font-size: 1.1rem;
}

.run-code-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #10b981;
  color: #ffffff;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.run-code-btn:hover {
  background: #059669;
  transform: translateY(-1px);
}

.run-code-btn:active {
  transform: translateY(0);
}

.code-description {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 6px;
}

.code-description p {
  margin: 0;
  color: #d1fae5;
  font-size: 0.9rem;
}
