import { useState } from 'react'
import { HelpCircle, X, Book, Code, Lightbulb, ExternalLink } from 'lucide-react'
import './HelpSystem.css'

const HelpSystem = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('shortcuts')

  const shortcuts = [
    { key: 'Ctrl + Enter', description: 'Run code' },
    { key: 'Ctrl + R', description: 'Reset code to initial state' },
    { key: 'Ctrl + /', description: 'Toggle comment' },
    { key: 'Ctrl + F', description: 'Find in code' },
    { key: 'Ctrl + S', description: 'Save progress (auto-saved)' },
    { key: 'F11', description: 'Toggle fullscreen preview' },
  ]

  const commonPatterns = [
    {
      title: 'Creating a Mesh',
      code: `const geometry = new THREE.BoxGeometry(1, 1, 1);
const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });
const mesh = createMesh(geometry, material);
scene.add(mesh);`
    },
    {
      title: 'Animation Loop',
      code: `animate(() => {
  // Update objects here
  mesh.rotation.y += 0.01;
  
  // Render is called automatically
});`
    },
    {
      title: 'Positioning Objects',
      code: `mesh.position.set(x, y, z);
mesh.rotation.set(x, y, z);
mesh.scale.set(x, y, z);`
    }
  ]

  const troubleshooting = [
    {
      problem: "Nothing appears on screen",
      solutions: [
        "Check if objects are added to the scene with scene.add()",
        "Ensure camera is positioned correctly (try camera.position.z = 5)",
        "Verify materials are visible (MeshBasicMaterial works without lights)"
      ]
    },
    {
      problem: "Objects appear black",
      solutions: [
        "Add lights to the scene for MeshStandardMaterial",
        "Use MeshBasicMaterial for objects that don't need lighting",
        "Check if lights are positioned correctly"
      ]
    },
    {
      problem: "Animation is choppy",
      solutions: [
        "Use requestAnimationFrame (handled by animate() function)",
        "Avoid creating new objects in the animation loop",
        "Check browser performance and close other tabs"
      ]
    }
  ]

  const resources = [
    {
      title: "Three.js Documentation",
      url: "https://threejs.org/docs/",
      description: "Official Three.js API documentation"
    },
    {
      title: "Three.js Examples",
      url: "https://threejs.org/examples/",
      description: "Interactive examples and demos"
    },
    {
      title: "Three.js Fundamentals",
      url: "https://threejsfundamentals.org/",
      description: "Comprehensive learning resource"
    },
    {
      title: "WebGL Fundamentals",
      url: "https://webglfundamentals.org/",
      description: "Understanding the underlying technology"
    }
  ]

  if (!isOpen) {
    return (
      <button 
        className="help-trigger"
        onClick={() => setIsOpen(true)}
        title="Help & Documentation"
      >
        <HelpCircle size={20} />
      </button>
    )
  }

  return (
    <div className="help-overlay">
      <div className="help-modal">
        <div className="help-header">
          <h2>Help & Documentation</h2>
          <button 
            className="help-close"
            onClick={() => setIsOpen(false)}
          >
            <X size={20} />
          </button>
        </div>

        <div className="help-tabs">
          <button 
            className={`help-tab ${activeTab === 'shortcuts' ? 'active' : ''}`}
            onClick={() => setActiveTab('shortcuts')}
          >
            <Code size={16} />
            Shortcuts
          </button>
          <button 
            className={`help-tab ${activeTab === 'patterns' ? 'active' : ''}`}
            onClick={() => setActiveTab('patterns')}
          >
            <Lightbulb size={16} />
            Patterns
          </button>
          <button 
            className={`help-tab ${activeTab === 'troubleshooting' ? 'active' : ''}`}
            onClick={() => setActiveTab('troubleshooting')}
          >
            <HelpCircle size={16} />
            Troubleshooting
          </button>
          <button 
            className={`help-tab ${activeTab === 'resources' ? 'active' : ''}`}
            onClick={() => setActiveTab('resources')}
          >
            <Book size={16} />
            Resources
          </button>
        </div>

        <div className="help-content">
          {activeTab === 'shortcuts' && (
            <div className="shortcuts-list">
              <h3>Keyboard Shortcuts</h3>
              {shortcuts.map((shortcut, index) => (
                <div key={index} className="shortcut-item">
                  <kbd>{shortcut.key}</kbd>
                  <span>{shortcut.description}</span>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'patterns' && (
            <div className="patterns-list">
              <h3>Common Code Patterns</h3>
              {commonPatterns.map((pattern, index) => (
                <div key={index} className="pattern-item">
                  <h4>{pattern.title}</h4>
                  <pre><code>{pattern.code}</code></pre>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'troubleshooting' && (
            <div className="troubleshooting-list">
              <h3>Common Issues</h3>
              {troubleshooting.map((item, index) => (
                <div key={index} className="troubleshooting-item">
                  <h4>{item.problem}</h4>
                  <ul>
                    {item.solutions.map((solution, sIndex) => (
                      <li key={sIndex}>{solution}</li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'resources' && (
            <div className="resources-list">
              <h3>Learning Resources</h3>
              {resources.map((resource, index) => (
                <a 
                  key={index} 
                  href={resource.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="resource-item"
                >
                  <div className="resource-content">
                    <h4>{resource.title}</h4>
                    <p>{resource.description}</p>
                  </div>
                  <ExternalLink size={16} />
                </a>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default HelpSystem
