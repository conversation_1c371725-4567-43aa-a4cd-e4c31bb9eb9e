import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import { ChevronLeft, ChevronRight, Play, RotateCcw, CheckCircle, Circle, ArrowDown, ExternalLink, Book, ChevronUp, Minimize2, Maximize2 } from 'lucide-react'
import CodeEditor from '../components/CodeEditor'
import ThreeJSViewer from '../components/ThreeJSViewer'
import EnhancedDocumentation from '../components/EnhancedDocumentation'
import LoadingSpinner from '../components/LoadingSpinner'
import LearningCards from '../components/LearningCards'
import AILessonGenerator from '../components/AILessonGenerator'
import { getLessonData } from '../data/lessons'
import { markLessonStarted, saveLessonCode, getLessonCode } from '../utils/progress'
import './NewLesson.css'

const NewLesson = () => {
  const { lessonId } = useParams()
  const [lesson, setLesson] = useState(null)
  const [code, setCode] = useState('')
  const [isRunning, setIsRunning] = useState(false)
  const [error, setError] = useState(null)
  const [codeVersion, setCodeVersion] = useState(1)
  const [viewerHeight, setViewerHeight] = useState(50)
  const [isDragging, setIsDragging] = useState(false)
  const [editorReloadKey, setEditorReloadKey] = useState(0)
  const [currentStep, setCurrentStep] = useState(0)
  const [completedSteps, setCompletedSteps] = useState(new Set())
  const [activeTab, setActiveTab] = useState('steps')
  const [isLearningCollapsed, setIsLearningCollapsed] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    // Clear canvas when switching between lessons
    console.log('🧹 Clearing canvas for lesson change...')
    const clearEvent = new CustomEvent('clearCanvasForLessonChange')
    window.dispatchEvent(clearEvent)

    const lessonData = getLessonData(parseInt(lessonId))
    if (lessonData) {
      setLesson(lessonData)
      setCode(lessonData.initialCode || '')
      markLessonStarted(parseInt(lessonId))

      // Load saved code, but prefer initial code for fresh start
      const savedCode = getLessonCode(parseInt(lessonId))
      if (savedCode) {
        setCode(savedCode)
      }

      // Auto-run the initial code after a short delay to ensure everything is loaded
      setTimeout(() => {
        console.log('🚀 Auto-running lesson code on load...')
        setCodeVersion(prev => prev + 1)
      }, 500)
    }
  }, [lessonId])

  // Detect mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 1024)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Handle auto-run for AI-generated code
  useEffect(() => {
    const handleRunGeneratedCode = () => {
      console.log('🤖 Auto-running AI-generated code...')
      handleRunCode()
    }

    window.addEventListener('runGeneratedCode', handleRunGeneratedCode)
    return () => window.removeEventListener('runGeneratedCode', handleRunGeneratedCode)
  }, [])

  // Re-execute code when learning panel collapse state changes
  useEffect(() => {
    if (lesson && code && !isRunning) {
      // Add a small delay to ensure the layout has finished transitioning
      const timer = setTimeout(() => {
        console.log('🔄 Learning panel collapsed/expanded - re-executing code for proper sizing...')

        // Trigger code re-execution to properly resize the Three.js renderer
        handleRunCode()

        // Also dispatch resize event as backup
        setTimeout(() => {
          const event = new CustomEvent('resizeViewer')
          window.dispatchEvent(event)
        }, 100)
      }, 350) // Slightly longer than the CSS transition (300ms)

      return () => clearTimeout(timer)
    }
  }, [isLearningCollapsed])

  // Save code periodically
  useEffect(() => {
    if (lesson && code) {
      const timer = setTimeout(() => {
        saveLessonCode(lesson.id, code)
      }, 2000)
      return () => clearTimeout(timer)
    }
  }, [code, lesson])

  const handleRunCode = () => {
    setIsRunning(true)
    setError(null)
    setCodeVersion(prev => prev + 1)
  }

  const handleCodeChange = (newCode) => {
    setCode(newCode)
  }

  const handleCodeError = (error) => {
    console.error('❌ Code execution error:', error)
    setError(error.message || 'An error occurred while running the code')
    setIsRunning(false)
  }

  const handleCodeSuccess = () => {
    console.log('✅ Code execution successful!')
    setIsRunning(false)
  }

  const handleStepComplete = (stepIndex) => {
    const newCompleted = new Set(completedSteps)
    if (newCompleted.has(stepIndex)) {
      newCompleted.delete(stepIndex)
    } else {
      newCompleted.add(stepIndex)
    }
    setCompletedSteps(newCompleted)
    
    // Auto-advance to next step if this one is completed
    if (!completedSteps.has(stepIndex) && stepIndex === currentStep) {
      const nextStep = stepIndex + 1
      if (nextStep < lesson.learningPath.length) {
        setTimeout(() => setCurrentStep(nextStep), 500)
      }
    }
  }

  const handleCodeInsert = (codeToInsert) => {
    setCode(codeToInsert)
    setCodeVersion(prev => prev + 1)
  }

  const scrollToStep = (stepIndex) => {
    setCurrentStep(stepIndex)
    const element = document.getElementById(`step-${stepIndex}`)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }

  // Resizer functionality (same as before)
  const handleMouseDown = (e) => {
    setIsDragging(true)
    e.preventDefault()
  }

  const handleMouseUp = () => {
    setIsDragging(false)
    setEditorReloadKey(prev => prev + 1)
    setTimeout(() => {
      handleRunCode()
    }, 200)
  }

  useEffect(() => {
    if (isDragging) {
      const handleGlobalMouseMove = (e) => {
        const editorPanel = document.querySelector('.editor-panel')
        if (!editorPanel) return
        
        const rect = editorPanel.getBoundingClientRect()
        const y = e.clientY - rect.top
        const newHeight = Math.max(20, Math.min(80, (y / rect.height) * 100))
        
        setViewerHeight(newHeight)
      }

      const handleGlobalMouseUp = () => {
        setIsDragging(false)
        setEditorReloadKey(prev => prev + 1)
        setTimeout(() => {
          handleRunCode()
        }, 200)
      }

      document.addEventListener('mousemove', handleGlobalMouseMove)
      document.addEventListener('mouseup', handleGlobalMouseUp)

      return () => {
        document.removeEventListener('mousemove', handleGlobalMouseMove)
        document.removeEventListener('mouseup', handleGlobalMouseUp)
      }
    }
  }, [isDragging])

  if (!lesson) {
    return (
      <div className="lesson-loading">
        <LoadingSpinner size="large" text="Loading lesson..." />
      </div>
    )
  }

  const prevLessonId = parseInt(lessonId) > 1 ? parseInt(lessonId) - 1 : null
  const nextLessonId = parseInt(lessonId) < 12 ? parseInt(lessonId) + 1 : null
  const steps = lesson.learningPath || []
  const completedCount = completedSteps.size

  return (
    <div className="new-lesson">
      {/* Lesson Header - Responsive */}
      <div className={`lesson-header ${isMobile ? 'mobile' : 'desktop'}`}>
        {isMobile ? (
          /* Compact Mobile Header - Single Line */
          <div className="mobile-header">
            {prevLessonId && (
              <Link to={`/lesson/${prevLessonId}`} className="mobile-nav-btn">
                <ChevronLeft size={14} />
              </Link>
            )}
            <div className="mobile-lesson-info">
              <span className="mobile-title">{lesson.title}</span>
              <span className={`mobile-difficulty ${lesson.difficulty.toLowerCase()}`}>
                {lesson.difficulty}
              </span>
            </div>
            {nextLessonId && (
              <Link to={`/lesson/${nextLessonId}`} className="mobile-nav-btn">
                <ChevronRight size={14} />
              </Link>
            )}
          </div>
        ) : (
          /* Desktop Header - Full Size */
          <div className="lesson-nav">
            {prevLessonId && (
              <Link to={`/lesson/${prevLessonId}`} className="nav-button">
                <ChevronLeft size={20} />
                Previous
              </Link>
            )}
            <div className="lesson-info">
              <h1>{lesson.title}</h1>
              <div className="lesson-meta">
                <span className={`difficulty ${lesson.difficulty.toLowerCase()}`}>
                  {lesson.difficulty}
                </span>
              </div>
            </div>
            {nextLessonId && (
              <Link to={`/lesson/${nextLessonId}`} className="nav-button">
                Next
                <ChevronRight size={20} />
              </Link>
            )}
          </div>
        )}
      </div>

      {/* Main Content - Two Column Layout */}
      <div className="lesson-content">
        {/* Left Column - Learning Steps (Collapsible on Mobile) */}
        <div className={`learning-column ${isLearningCollapsed ? 'collapsed' : ''}`}>
          <div className="learning-header">
            <div className="learning-tabs">
              <button
                className={`learning-tab ${activeTab === 'steps' ? 'active' : ''}`}
                onClick={() => setActiveTab('steps')}
              >
                <CheckCircle size={16} />
                Learning Steps
              </button>
              <button
                className={`learning-tab ${activeTab === 'docs' ? 'active' : ''}`}
                onClick={() => setActiveTab('docs')}
              >
                <Book size={16} />
                Three.js Docs
              </button>
              {isMobile && (
                <button
                  className="learning-collapse-btn"
                  onClick={() => setIsLearningCollapsed(!isLearningCollapsed)}
                  title={isLearningCollapsed ? 'Expand learning panel' : 'Collapse learning panel'}
                >
                  {isLearningCollapsed ? <Maximize2 size={16} /> : <Minimize2 size={16} />}
                </button>
              )}
            </div>
            {!isLearningCollapsed && (
              <div className="tab-description">
                {activeTab === 'steps' ? (
                  <p>Follow along step-by-step to master this lesson</p>
                ) : (
                  <p>Reference documentation and examples</p>
                )}
              </div>
            )}
          </div>

          {!isLearningCollapsed && (
            <div className="tab-content">
              {activeTab === 'steps' ? (
                parseInt(lessonId) === 12 ? (
                  <AILessonGenerator onCodeUpdate={handleCodeInsert} />
                ) : (
                  <LearningCards
                    lesson={lesson}
                    onCodeInsert={handleCodeInsert}
                  />
                )
              ) : (
                <div className="docs-container">
                  <EnhancedDocumentation lesson={lesson} />
                </div>
              )}
            </div>
          )}
        </div>

        {/* Right Column - Code Editor & 3D Viewer */}
        <div className={`editor-column ${isLearningCollapsed ? 'expanded' : ''}`}>
          <div className="editor-panel">
            {/* Three.js Viewer */}
            <div 
              className="viewer-section"
              style={{ height: `${viewerHeight}%` }}
            >
              <div className="viewer-header">
                <h3>3D Preview</h3>
                <button 
                  className="run-button"
                  onClick={handleRunCode}
                  disabled={isRunning}
                >
                  <Play size={16} />
                  {isRunning ? 'Running...' : 'Run Code'}
                </button>
              </div>
              <ThreeJSViewer
                code={code}
                codeVersion={codeVersion}
                onError={handleCodeError}
                onSuccess={handleCodeSuccess}
              />
              {error && (
                <div className="error-display">
                  <strong>Error:</strong> {error}
                </div>
              )}
            </div>

            {/* Resizer Handle */}
            <div 
              className={`resizer ${isDragging ? 'dragging' : ''}`}
              onMouseDown={handleMouseDown}
            >
              <div className="resizer-line"></div>
            </div>

            {/* Code Editor */}
            <div 
              className="code-section"
              style={{ height: `${100 - viewerHeight}%` }}
            >
              <div className="code-header">
                <h3>Code Editor</h3>
                <button 
                  className="reset-button"
                  onClick={() => {
                    setCode(lesson.initialCode || '')
                    setCodeVersion(prev => prev + 1)
                  }}
                >
                  <RotateCcw size={16} />
                  Reset
                </button>
              </div>
              <CodeEditor
                value={code}
                onChange={handleCodeChange}
                language="javascript"
                forceReload={editorReloadKey}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default NewLesson
