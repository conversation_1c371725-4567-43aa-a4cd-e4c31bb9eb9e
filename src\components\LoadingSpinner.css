.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.loading-spinner-container.small {
  gap: 0.5rem;
}

.loading-spinner-container.large {
  gap: 1.5rem;
}

.loading-spinner {
  position: relative;
  display: inline-block;
}

.loading-spinner-container.small .loading-spinner {
  width: 24px;
  height: 24px;
}

.loading-spinner-container.medium .loading-spinner {
  width: 40px;
  height: 40px;
}

.loading-spinner-container.large .loading-spinner {
  width: 60px;
  height: 60px;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid transparent;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
}

.spinner-ring:nth-child(2) {
  border-top-color: #60a5fa;
  animation-delay: -0.4s;
  animation-duration: 1.6s;
}

.spinner-ring:nth-child(3) {
  border-top-color: #93c5fd;
  animation-delay: -0.8s;
  animation-duration: 2s;
}

.loading-text {
  color: #9ca3af;
  font-size: 0.875rem;
  font-weight: 500;
}

.loading-spinner-container.small .loading-text {
  font-size: 0.75rem;
}

.loading-spinner-container.large .loading-text {
  font-size: 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Pulsing variant */
.loading-spinner-container.pulse .spinner-ring {
  animation: pulse 1.5s ease-in-out infinite;
  border: 2px solid #3b82f6;
  border-radius: 50%;
}

.loading-spinner-container.pulse .spinner-ring:nth-child(2) {
  animation-delay: -0.5s;
  border-color: #60a5fa;
}

.loading-spinner-container.pulse .spinner-ring:nth-child(3) {
  animation-delay: -1s;
  border-color: #93c5fd;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}
