import { useState } from 'react'
import { ExternalLink, Info } from 'lucide-react'
import './DocumentationTooltip.css'

const DocumentationTooltip = ({ term, description, docUrl, children }) => {
  const [isVisible, setIsVisible] = useState(false)

  return (
    <span className="doc-tooltip-container">
      <span 
        className="doc-tooltip-trigger"
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
        onClick={() => setIsVisible(!isVisible)}
      >
        {children}
        <Info size={12} className="doc-tooltip-icon" />
      </span>
      
      {isVisible && (
        <div className="doc-tooltip">
          <div className="doc-tooltip-content">
            <h4>{term}</h4>
            <p>{description}</p>
            {docUrl && (
              <a 
                href={docUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="doc-tooltip-link"
              >
                <ExternalLink size={14} />
                View Documentation
              </a>
            )}
          </div>
        </div>
      )}
    </span>
  )
}

export default DocumentationTooltip
