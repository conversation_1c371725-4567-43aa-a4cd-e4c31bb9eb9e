// API endpoint for generating AI lessons
// This is a Vercel serverless function

const fetch = require('node-fetch');

module.exports = async function handler(req, res) {
  console.log('🚀 Function started - method:', req.method);

  try {
    // Enable CORS
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    console.log('✅ CORS headers set');

    if (req.method === 'OPTIONS') {
      console.log('✅ OPTIONS request handled');
      res.status(200).end();
      return;
    }

    if (req.method === 'GET') {
      console.log('✅ GET request - returning test response');
      return res.status(200).json({
        message: 'API endpoint is working!',
        method: req.method,
        timestamp: new Date().toISOString(),
        note: 'Use POST method with systemPrompt and userPrompt to generate lessons'
      });
    }

    if (req.method !== 'POST') {
      console.log('❌ Invalid method:', req.method);
      return res.status(405).json({ error: 'Method not allowed' });
    }

    console.log('✅ Method validation passed');

    // Parse request body
    const { systemPrompt, userPrompt } = req.body;

    if (!systemPrompt || !userPrompt) {
      console.log('❌ Missing prompts:', { systemPrompt: !!systemPrompt, userPrompt: !!userPrompt });
      return res.status(400).json({ error: 'Missing required prompts' });
    }

    console.log('✅ Prompts received:', {
      systemPromptLength: systemPrompt.length,
      userPrompt: userPrompt
    });

    // Check for OpenAI API key
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      console.error('❌ OpenAI API key not found in environment variables');
      return res.status(500).json({ error: 'OpenAI API key not configured' });
    }

    console.log('✅ API Key found, length:', apiKey.length);

    // Call OpenAI API
    console.log('🤖 Calling OpenAI API...');
    const requestBody = {
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: userPrompt
        }
      ],
      max_tokens: 3000,
      temperature: 0.7,
    };

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    console.log('📥 OpenAI response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      console.error('OpenAI API error:', response.status, errorData);
      return res.status(500).json({
        error: 'Failed to generate lesson content',
        details: errorData.error?.message || 'Unknown OpenAI error'
      });
    }

    const data = await response.json();
    const lessonContent = data.choices[0]?.message?.content;

    if (!lessonContent) {
      console.log('❌ No content in OpenAI response');
      return res.status(500).json({ error: 'No content generated' });
    }

    // Try to parse the JSON response
    let lessonData;
    try {
      lessonData = JSON.parse(lessonContent);
      console.log('✅ Successfully parsed lesson JSON');
    } catch (parseError) {
      console.error('❌ Failed to parse AI response:', parseError);
      return res.status(500).json({ error: 'Invalid lesson format generated' });
    }

    console.log('🎉 Lesson generated successfully');
    return res.status(200).json({ lesson: lessonData });

  } catch (error) {
    console.error('💥 Function error:', error);
    return res.status(500).json({
      error: 'Function failed',
      details: error.message
    });
  }
}
