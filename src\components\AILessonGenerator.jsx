import React, { useState } from 'react'
import { Prism as <PERSON>yntax<PERSON>ighlighter } from 'react-syntax-highlighter'
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { <PERSON>, <PERSON>rk<PERSON>, Loader, RefreshCw } from 'lucide-react'
import Markdown<PERSON>enderer from './MarkdownRenderer'
import './AILessonGenerator.css'



// Mock lesson generator for demo purposes
const generateMockLesson = (userPrompt) => {
  const isSpaceTheme = userPrompt.toLowerCase().includes('solar') || userPrompt.toLowerCase().includes('planet') || userPrompt.toLowerCase().includes('space')
  const isParticleTheme = userPrompt.toLowerCase().includes('particle') || userPrompt.toLowerCase().includes('star') || userPrompt.toLowerCase().includes('effect')

  if (isSpaceTheme) {
    return {
      title: "Solar System Simulation",
      description: "Learn to create an interactive 3D solar system with orbiting planets, realistic lighting, and smooth animations.",
      code: `// Solar System Simulation
// Create a beautiful 3D solar system with orbiting planets

// Create the sun (center of our solar system)
const sunGeometry = new THREE.SphereGeometry(1, 32, 32);
const sunMaterial = new THREE.MeshBasicMaterial({
  color: 0xffff00,
  emissive: 0xffaa00
});
const sun = createMesh(sunGeometry, sunMaterial);
scene.add(sun);

// Create planets with different sizes and colors
const planets = [];
const planetData = [
  { name: 'Mercury', size: 0.2, distance: 2, color: 0x8c7853, speed: 0.04 },
  { name: 'Venus', size: 0.3, distance: 3, color: 0xffc649, speed: 0.03 },
  { name: 'Earth', size: 0.35, distance: 4, color: 0x6b93d6, speed: 0.02 },
  { name: 'Mars', size: 0.25, distance: 5, color: 0xc1440e, speed: 0.015 }
];

planetData.forEach(data => {
  const geometry = new THREE.SphereGeometry(data.size, 16, 16);
  const material = new THREE.MeshStandardMaterial({ color: data.color });
  const planet = createMesh(geometry, material);

  // Position planet at its orbital distance
  planet.position.x = data.distance;
  planet.userData = {
    distance: data.distance,
    speed: data.speed,
    angle: Math.random() * Math.PI * 2
  };

  planets.push(planet);
  scene.add(planet);
});

// Add lighting
const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
scene.add(ambientLight);

const sunLight = new THREE.PointLight(0xffffff, 2, 50);
sunLight.position.set(0, 0, 0);
scene.add(sunLight);

// Position camera for good view
camera.position.set(8, 6, 8);
camera.lookAt(0, 0, 0);

// Animation loop - make planets orbit the sun
animate(() => {
  // Rotate the sun
  sun.rotation.y += 0.01;

  // Orbit planets around the sun
  planets.forEach(planet => {
    planet.userData.angle += planet.userData.speed;
    planet.position.x = Math.cos(planet.userData.angle) * planet.userData.distance;
    planet.position.z = Math.sin(planet.userData.angle) * planet.userData.distance;

    // Rotate planets on their axis
    planet.rotation.y += 0.02;
  });
});`,
      steps: [
        {
          title: "Step 1: Create the Sun",
          content: "Let's start by creating the central star of our solar system! ☀️\n\n🎯 **SUN CREATION:**\n📝 The sun is the largest object in our solar system\n✨ Uses emissive materials to glow like a real star\n💡 Positioned at the center (0, 0, 0)\n💻 `new THREE.SphereGeometry(1, 32, 32)` // creates smooth sphere\n\n🌟 **EMISSIVE MATERIALS:**\n🔥 Makes objects glow without external lighting\n🎨 Perfect for stars, fire, magical effects\n⚡ Combines color and emissive properties",
          codeExample: `const sunGeometry = new THREE.SphereGeometry(1, 32, 32);
const sunMaterial = new THREE.MeshBasicMaterial({
  color: 0xffff00,
  emissive: 0xffaa00
});
const sun = createMesh(sunGeometry, sunMaterial);
scene.add(sun);`,
          explanation: "MeshBasicMaterial with emissive property makes the sun glow like a real star."
        },
        {
          title: "Step 2: Create Planets",
          content: "Now let's add planets with different sizes and orbital distances! 🪐\n\n🌍 **PLANET PROPERTIES:**\n📏 Each planet has unique size and color\n🔄 Different orbital distances from sun\n⚡ Varying orbital speeds (closer = faster)\n💻 `new THREE.MeshStandardMaterial()` // responds to lighting\n\n🎯 **ORBITAL MECHANICS:**\n📐 Use trigonometry for circular orbits\n🔄 Math.cos() and Math.sin() create smooth motion\n⏰ Different speeds create realistic solar system",
          codeExample: `const planetData = [
  { name: 'Mercury', size: 0.2, distance: 2, color: 0x8c7853, speed: 0.04 },
  { name: 'Venus', size: 0.3, distance: 3, color: 0xffc649, speed: 0.03 },
  { name: 'Earth', size: 0.35, distance: 4, color: 0x6b93d6, speed: 0.02 }
];`,
          explanation: "We store planet data in an array to easily create multiple planets with different properties."
        }
      ],
      concepts: [
        {
          name: "Orbital Mechanics",
          description: "Using trigonometry to create realistic planetary orbits around the sun"
        },
        {
          name: "Point Lighting",
          description: "Using PointLight to simulate the sun's illumination of planets"
        }
      ]
    }
  }

  // Default lesson for other prompts
  return {
    title: "Custom Three.js Scene",
    description: `A custom Three.js lesson based on your request: "${userPrompt}"`,
    code: `// Custom Three.js Scene
// Generated based on your prompt: ${userPrompt}

// Create main geometry
const geometry = new THREE.BoxGeometry(1, 1, 1);
const material = new THREE.MeshStandardMaterial({
  color: 0x0066ff,
  roughness: 0.3,
  metalness: 0.1
});
const mainObject = createMesh(geometry, material);
scene.add(mainObject);

// Add lighting
const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
scene.add(ambientLight);

const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
directionalLight.position.set(5, 5, 5);
scene.add(directionalLight);

// Position camera
camera.position.set(3, 3, 5);
camera.lookAt(0, 0, 0);

// Animation
animate(() => {
  mainObject.rotation.x += 0.01;
  mainObject.rotation.y += 0.01;
});`,
    steps: [
      {
        title: "Step 1: Basic Setup",
        content: "This is a basic Three.js scene setup! 🎯\n\n🏗️ **SCENE ELEMENTS:**\n📦 Every scene needs geometry (shape)\n🎨 Materials define appearance\n💡 Lighting makes objects visible\n⚡ Animation brings everything to life\n💻 `createMesh(geometry, material)` // helper function\n\n🎯 **WHY THIS WORKS:**\n✨ Follows the magic formula: Geometry + Material = Mesh\n🌟 Standard workflow for all 3D objects",
        codeExample: `const geometry = new THREE.BoxGeometry(1, 1, 1);
const material = new THREE.MeshStandardMaterial({ color: 0x0066ff });
const object = createMesh(geometry, material);
scene.add(object);`,
        explanation: "This creates a basic 3D object and adds it to the scene using the fundamental Three.js pattern."
      }
    ],
    concepts: [
      {
        name: "Basic 3D Scene",
        description: "Understanding the fundamental components of a Three.js scene"
      }
    ]
  }
}

const AILessonGenerator = ({ onCodeUpdate }) => {
  const [prompt, setPrompt] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedLesson, setGeneratedLesson] = useState(null)
  const [error, setError] = useState(null)

  const examplePrompts = [
    "Create a solar system with orbiting planets and moons",
    "Build a particle system with floating stars and nebula effects",
    "Make an interactive 3D maze game with collision detection",
    "Design a procedural terrain generator with mountains and valleys",
    "Create a realistic water simulation with waves and reflections",
    "Build a 3D music visualizer that responds to audio",
    "Make a physics-based bouncing ball simulation",
    "Create an animated 3D character with walking animation"
  ]

  const generateLesson = async () => {
    if (!prompt.trim()) {
      setError('Please enter a prompt describing what you want to learn!')
      return
    }

    setIsGenerating(true)
    setError(null)

    try {
      // Create the system prompt for GPT
      const systemPrompt = `You are an expert Three.js instructor creating lessons for high school students. Create a complete interactive lesson that matches the existing lesson format. Return ONLY valid JSON in this exact format:

{
  "title": "Lesson Title (keep it engaging and descriptive)",
  "description": "Brief description of what the lesson teaches and why it's useful",
  "code": "// Complete Three.js code that works in the provided environment\\n// Use createMesh() helper function\\n// Include scene, camera, lighting, and animation\\n// Add detailed comments explaining each step",
  "steps": [
    {
      "title": "Step 1: Setup",
      "content": "Educational content with proper formatting. Use emojis, bullet points, and clear explanations like existing lessons. Format like:\\n\\n🎯 **MAIN CONCEPT:**\\n📝 Bullet point explanations\\n💻 \`code.example()\` // inline code with comments\\n\\nMake it engaging for students!",
      "codeExample": "// Code snippet for this specific step only",
      "explanation": "Brief explanation of what this code accomplishes"
    }
  ],
  "concepts": [
    {
      "name": "Concept Name",
      "description": "Clear explanation of what this concept teaches students"
    }
  ]
}

CRITICAL REQUIREMENTS:
1. **Code Environment**: scene, camera, renderer already exist - DO NOT redeclare them
2. **Helper Functions**: Use createMesh(geometry, material) instead of new THREE.Mesh()
3. **Lighting**: Always add lights with scene.add(light) - AmbientLight and DirectionalLight
4. **Camera**: Position with camera.position.set() and camera.lookAt() - do not create new camera
5. **Animation**: Use animate(() => { /* animation code */ }) function
6. **No Redeclaration**: Never use 'const scene =', 'const camera =', 'const renderer ='
7. **Comments**: Add detailed // comments explaining each line
8. **Student-Friendly**: Write for high school level, use engaging language
9. **Format Matching**: Use emojis, bullet points, and formatting like existing lessons
10. **Complete Code**: Ensure all code is runnable and complete
11. **Educational Value**: Make it progressive and teach real concepts
12. **JSON Only**: Return ONLY the JSON object, no other text

ENVIRONMENT VARIABLES AVAILABLE:
- scene (THREE.Scene) - already created
- camera (THREE.PerspectiveCamera) - already created
- renderer (THREE.WebGLRenderer) - already created
- createMesh(geometry, material) - helper function
- animate(callback) - animation function

Example content formatting:
"🎯 **MAIN CONCEPT:**\\n📝 Explanation point\\n💻 \`new THREE.BoxGeometry(1, 1, 1)\` // creates a cube\\n\\n🌟 **WHY IT MATTERS:**\\n✨ Real-world application"

Make the lesson engaging, educational, and perfectly formatted for the existing system!`

      const userPrompt = `Create a Three.js lesson about: ${prompt}`

      // For demo purposes, use a mock response if API is not available
      let lessonData

      // Always use the API endpoint for both local and production
      try {
        console.log('Calling /api/generate-lesson endpoint...')
        const response = await fetch('/api/generate-lesson', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            systemPrompt,
            userPrompt
          })
        })

        if (response.ok) {
          const data = await response.json()
          lessonData = typeof data.lesson === 'string' ? JSON.parse(data.lesson) : data.lesson
          console.log('✅ Successfully generated lesson via API')
        } else {
          const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
          console.error('❌ API Error:', response.status, errorData)
          throw new Error(errorData.error || `API Error: ${response.status}`)
        }
      } catch (apiError) {
        console.log('❌ API failed, using mock lesson:', apiError.message)
        lessonData = generateMockLesson(prompt)
      }

      setGeneratedLesson(lessonData)
      
      // Update the main code editor with the generated code
      if (onCodeUpdate && lessonData.code) {
        onCodeUpdate(lessonData.code)
      }

    } catch (err) {
      console.error('Error generating lesson:', err)
      setError(err.message || 'Failed to generate lesson. Please try again.')
    } finally {
      setIsGenerating(false)
    }
  }

  const useExamplePrompt = (example) => {
    setPrompt(example)
  }

  const resetLesson = () => {
    setGeneratedLesson(null)
    setPrompt('')
    setError(null)
  }

  return (
    <div className="ai-lesson-generator">
      <div className="generator-header">
        <div className="header-content">
          <Sparkles className="header-icon" />
          <h3>AI Lesson Generator</h3>
          <p>Describe what you want to learn and AI will create a custom Three.js lesson!</p>
        </div>
      </div>

      <div className="prompt-section">
        <div className="prompt-input-container">
          <textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="Describe the Three.js lesson you want to create..."
            className="prompt-input"
            rows={3}
            disabled={isGenerating}
          />
          <div className="prompt-actions">
            <button 
              onClick={generateLesson}
              disabled={isGenerating || !prompt.trim()}
              className="generate-btn"
            >
              {isGenerating ? (
                <>
                  <Loader className="spinning" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles />
                  Generate Lesson
                </>
              )}
            </button>
            {generatedLesson && (
              <button onClick={resetLesson} className="reset-btn">
                <RefreshCw />
                New Lesson
              </button>
            )}
          </div>
        </div>

        {error && (
          <div className="error-message">
            <p>❌ {error}</p>
          </div>
        )}

        {!generatedLesson && (
          <div className="example-prompts">
            <h4>💡 Try these example prompts:</h4>
            <div className="prompt-examples">
              {examplePrompts.map((example, index) => (
                <button
                  key={index}
                  onClick={() => useExamplePrompt(example)}
                  className="example-prompt"
                  disabled={isGenerating}
                >
                  "{example}"
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {generatedLesson && (
        <div className="generated-lesson">
          <div className="lesson-header">
            <h2>{generatedLesson.title}</h2>
            <p>{generatedLesson.description}</p>
          </div>

          {generatedLesson.concepts && generatedLesson.concepts.length > 0 && (
            <div className="lesson-concepts">
              <h3>🎯 Key Concepts</h3>
              <div className="concepts-grid">
                {generatedLesson.concepts.map((concept, index) => (
                  <div key={index} className="concept-card">
                    <h4>{concept.name}</h4>
                    <p>{concept.description}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {generatedLesson.code && (
            <div className="lesson-main-code">
              <div className="code-header">
                <h3>🚀 Complete Code Example</h3>
                <button
                  onClick={() => {
                    if (onCodeUpdate) {
                      onCodeUpdate(generatedLesson.code)
                      // Auto-run the code after inserting it
                      setTimeout(() => {
                        // Trigger code execution by dispatching a custom event
                        window.dispatchEvent(new CustomEvent('runGeneratedCode'))
                      }, 100)
                    }
                  }}
                  className="run-code-btn"
                >
                  <Play size={16} />
                  Run This Code
                </button>
              </div>
              <SyntaxHighlighter
                language="javascript"
                style={vscDarkPlus}
                customStyle={{
                  margin: 0,
                  borderRadius: '4px',
                  fontSize: '12px',
                  lineHeight: '1.4',
                  maxHeight: '400px',
                  overflow: 'auto'
                }}
              >
                {generatedLesson.code}
              </SyntaxHighlighter>
              <div className="code-description">
                <p>💡 This is the complete working example for this lesson. Click "Run This Code" to see it in action!</p>
              </div>
            </div>
          )}

          {generatedLesson.steps && generatedLesson.steps.length > 0 && (
            <div className="lesson-steps">
              <h3>📚 Learning Steps</h3>
              {generatedLesson.steps.map((step, index) => (
                <div key={index} className="lesson-step">
                  <h4>{step.title}</h4>
                  <MarkdownRenderer content={step.content} />
                  
                  {step.codeExample && (
                    <div className="step-code">
                      <SyntaxHighlighter
                        language="javascript"
                        style={vscDarkPlus}
                        customStyle={{
                          margin: 0,
                          borderRadius: '4px',
                          fontSize: '11px',
                          lineHeight: '1.4'
                        }}
                      >
                        {step.codeExample}
                      </SyntaxHighlighter>
                      {step.explanation && (
                        <div className="code-explanation">
                          <strong>💡</strong> {step.explanation}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default AILessonGenerator
