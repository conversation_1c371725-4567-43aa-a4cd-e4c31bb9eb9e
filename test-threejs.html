<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        #container {
            display: flex;
            gap: 20px;
            height: 80vh;
        }
        
        #editor {
            flex: 1;
            background: #2d2d2d;
            padding: 10px;
            border-radius: 8px;
        }
        
        #viewer {
            flex: 1;
            background: #1a1a1a;
            border-radius: 8px;
            position: relative;
        }
        
        textarea {
            width: 100%;
            height: 300px;
            background: #2d2d2d;
            color: white;
            border: 1px solid #444;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
        }
        
        button {
            background: #0066ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        
        button:hover {
            background: #0052cc;
        }
        
        #error {
            color: #ff4444;
            margin-top: 10px;
            padding: 10px;
            background: rgba(255, 68, 68, 0.1);
            border-radius: 4px;
            display: none;
        }
        
        #status {
            color: #44ff44;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>🎯 Three.js Execution Test</h1>
    
    <div id="container">
        <div id="editor">
            <h3>Code Editor</h3>
            <textarea id="code">// Create a simple rotating cube
const geometry = new THREE.BoxGeometry(1.5, 1.5, 1.5);
const material = new THREE.MeshBasicMaterial({ color: 0x0066ff });
const cube = new THREE.Mesh(geometry, material);

// Add the cube to the scene
scene.add(cube);

// Position the camera
camera.position.z = 4;

// Create an animation loop
animate(() => {
  // Rotate the cube
  cube.rotation.x += 0.01;
  cube.rotation.y += 0.01;
});</textarea>
            <br>
            <button onclick="runCode()">🚀 Run Code</button>
            <div id="error"></div>
            <div id="status"></div>
        </div>
        
        <div id="viewer">
            <h3>3D Viewer</h3>
            <!-- Three.js canvas will be added here -->
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        let scene, camera, renderer, animationId;
        
        // Initialize Three.js
        function initThreeJS() {
            console.log('🎬 Initializing Three.js...');
            
            // Create scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x1a1a1a);
            
            // Create camera
            const viewer = document.getElementById('viewer');
            camera = new THREE.PerspectiveCamera(
                75,
                viewer.clientWidth / viewer.clientHeight,
                0.1,
                1000
            );
            camera.position.set(0, 0, 5);
            
            // Create renderer
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(viewer.clientWidth, viewer.clientHeight);
            viewer.appendChild(renderer.domElement);
            
            // Add basic lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            scene.add(directionalLight);
            
            // Initial render
            renderer.render(scene, camera);
            
            console.log('✅ Three.js initialized successfully');
        }
        
        function clearScene() {
            // Remove all user-created objects
            const objectsToRemove = [];
            scene.traverse((child) => {
                if (child.userData?.userCreated || child.isMesh) {
                    objectsToRemove.push(child);
                }
            });
            
            objectsToRemove.forEach((obj) => {
                scene.remove(obj);
                if (obj.geometry) obj.geometry.dispose();
                if (obj.material) {
                    if (Array.isArray(obj.material)) {
                        obj.material.forEach(mat => mat.dispose());
                    } else {
                        obj.material.dispose();
                    }
                }
            });
            
            console.log(`🧹 Cleaned up ${objectsToRemove.length} objects`);
        }
        
        function runCode() {
            console.log('🚀 Running user code...');
            
            const code = document.getElementById('code').value;
            const errorDiv = document.getElementById('error');
            const statusDiv = document.getElementById('status');
            
            // Clear previous messages
            errorDiv.style.display = 'none';
            statusDiv.textContent = 'Executing...';
            
            try {
                // Stop any existing animation
                if (animationId) {
                    cancelAnimationFrame(animationId);
                    animationId = null;
                }
                
                // Clear previous objects
                clearScene();
                
                // Reset camera
                camera.position.set(0, 0, 5);
                camera.lookAt(0, 0, 0);
                
                // Create helper functions
                const animate = (callback) => {
                    if (animationId) {
                        cancelAnimationFrame(animationId);
                    }
                    
                    const loop = () => {
                        animationId = requestAnimationFrame(loop);
                        callback();
                        renderer.render(scene, camera);
                    };
                    loop();
                    console.log('🎬 Animation loop started');
                };
                
                // Execute user code
                const userFunction = new Function(
                    'THREE', 'scene', 'camera', 'renderer', 'animate',
                    code
                );
                userFunction(THREE, scene, camera, renderer, animate);
                
                // Render once
                renderer.render(scene, camera);
                
                statusDiv.textContent = '✅ Code executed successfully!';
                console.log('✅ Code execution completed');
                
            } catch (error) {
                console.error('❌ Code execution error:', error);
                errorDiv.textContent = `Error: ${error.message}`;
                errorDiv.style.display = 'block';
                statusDiv.textContent = '❌ Execution failed';
            }
        }
        
        // Initialize when page loads
        window.addEventListener('load', () => {
            initThreeJS();
            
            // Auto-run the initial code
            setTimeout(() => {
                console.log('🎯 Auto-running initial code...');
                runCode();
            }, 500);
        });
        
        // Handle window resize
        window.addEventListener('resize', () => {
            if (camera && renderer) {
                const viewer = document.getElementById('viewer');
                camera.aspect = viewer.clientWidth / viewer.clientHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(viewer.clientWidth, viewer.clientHeight);
            }
        });
    </script>
</body>
</html>
